package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.EngagementWorkflow
import com.connexin.pmx.server.models.Template
import com.connexin.pmx.server.models.dtos.CreateOrEditTemplateRequest
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.services.TemplateService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageImpl
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(AdminTemplateController::class)
@WithMockUser(authorities = ["admin.templates:read", "admin.templates:write"])
class AdminTemplateControllerTests {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var service: TemplateService

    @Test
    fun `findAll should return page of DTOs`() {
        every { service.findAll(any()) } answers {
            PageImpl(
                listOf(
                    Template(
                        id = "test",
                        name = "test",
                        workflow = EngagementWorkflow.CONFIRMATION,
                        variations = emptyMap()
                    )
                ),
                firstArg(),
                1
            )
        }

        mockMvc.perform(
            get("/api/v2/admin/templates")
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `getById should return 404`() {
        every { service.getById("test") } returns null

        mockMvc.perform(
            get("/api/v2/admin/templates/test")
        )
            .andExpect(status().isNotFound)
    }

    @Test
    fun `getById should return template`() {
        val expected = Template(
            id = "test",
            name = "test",
            workflow = EngagementWorkflow.CONFIRMATION,
            variations = emptyMap()
        )
        every { service.getById("test") } returns expected

        mockMvc.perform(
            get("/api/v2/admin/templates/test")
        )
            .andExpect(status().isOk)
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }
}