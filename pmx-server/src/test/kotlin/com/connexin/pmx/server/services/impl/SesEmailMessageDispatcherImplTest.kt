package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.TestFileUtil
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.services.*
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration
import java.time.Instant
import java.util.*

@ExtendWith(MockKExtension::class)
class SesEmailMessageDispatcherImplTest {
    @MockK
    lateinit var emailService: EmailService

    @MockK
    lateinit var subscriptionService: SubscriptionService

    @MockK
    lateinit var pmxMessageService: PmxMessageService

    private val urlGenerator = UrlGenerator("http://localhost:8080", "http://localhost:8080")

    private lateinit var sut: SesEmailMessageDispatcherImpl

    private val objectMapper = ObjectMapper().findAndRegisterModules()

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        sut = SesEmailMessageDispatcherImpl(
            emailService,
            objectMapper,
            subscriptionService,
            urlGenerator,
            pmxMessageService,
            10
        )

        customer = Customer(
            id = "1", name = "Test", organizationId = 1L,
            status = CustomerStatus.ENABLED
        )

        every { subscriptionService.getById(any()) } returns null
        every { subscriptionService.getById(UNSUBSCRIBED_ADDRESS) } returns SubscriptionPreference(
            id = UNSUBSCRIBED_ADDRESS,
            subscribed = false
        )
    }

    @Test
    fun `beforeRespond should accept when remoteId is found`() {
        val expectedRemoteId = "3788ec5c-db08-409d-9b85-5f642b87096a"
        every { pmxMessageService.getByRemoteId(expectedRemoteId) } returns
                PmxMessage(
                    id = "0000016cb87f5e313e4e13b0",
                    type = MessageType.EMAIL,
                    remoteId = expectedRemoteId,
                    status = MessageStatus.DISPATCHED,
                    customerId = "1",
                    message = "test",
                    sendAfter = Instant.EPOCH,
                    to = "<EMAIL>"
                )

        val actual = sut.beforeRespond(
            RespondContext(
                payload = TestFileUtil.getAsString("/ses/event_webhook.json"),
                type = MessageType.EMAIL
            )
        )

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull
        assertThat(actual.context.decodedPayload).isNotNull
        assertThat(actual.context.remoteId).isEqualTo(expectedRemoteId)
    }

    @Test
    fun `beforeResponse should not accept when email recipient is not found`() {
        val expectedRemoteId = "a3543fb9-cf17-40b6-92b6-ab08066bd4bf"
        every { pmxMessageService.getByRemoteId(expectedRemoteId) } returns
                PmxMessage(
                    id = "63206d1a6d821c506aad8aa4",
                    type = MessageType.EMAIL_BROADCAST,
                    remoteId = expectedRemoteId,
                    status = MessageStatus.DISPATCHED,
                    customerId = "1",
                    message = "test",
                    sendAfter = Instant.EPOCH,
                    emailRecipients = listOf(
                        PmxMessage.EmailRecipient(
                            address = "<EMAIL>",
                            status = MessageStatus.QUEUED,
                            // no remoteId
                        )
                    )
                )

        val actual = sut.beforeRespond(
            RespondContext(
                payload = TestFileUtil.getAsString("/ses/bulk_send_webhook.json"),
                type = MessageType.EMAIL_BROADCAST
            )
        )

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
        assertThat(actual.context).isNotNull
        assertThat(actual.context.decodedPayload).isNotNull
        assertThat(actual.context.remoteId).isEqualTo(expectedRemoteId)
    }

    @Test
    fun `beforeRespond should not accept if event could not be deserialized`() {
        val actual = sut.beforeRespond(
            RespondContext(
                payload = "test",
                type = MessageType.EMAIL
            )
        )

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
    }

    @Test
    fun `initiate should fail when email is unsubscribed`() {
        val actual = sut.initiate(
            message = PmxMessage(
                id = "test-id",
                customerId = "1",
                type = MessageType.EMAIL,
                status = MessageStatus.QUEUED,
                message = "test",
                sendAfter = Instant.now(),
                to = UNSUBSCRIBED_ADDRESS
            ),
            customer = customer
        )

        assertThat(actual.success).isFalse
        assertThat(actual.reason).isEqualTo(InitiateResult.Reason.CONSENT)
    }

    @Test
    fun `buildMessage appends unsubscribe link in english by default`() {
        val actual = sut.buildMessage(
            message = PmxMessage(
                id = "test-id",
                engagementId = "engagement-id",
                customerId = "1",
                type = MessageType.EMAIL,
                status = MessageStatus.QUEUED,
                message = "test",
                sendAfter = Instant.now(),
                to = "<EMAIL>"
            )
        )

        assertThat(actual).isEqualTo("test\n\nIf you would prefer not to receive further messages from this sender please follow the link and confirm your request. http://localhost:8080/unsubscribe?id=test-id&email=test%40test.com")
    }

    @Test
    fun `buildMessage appends unsubscribe link using specified language`() {
        val actual = sut.buildMessage(
            message = PmxMessage(
                id = "test-id",
                engagementId = "engagement-id",
                customerId = "1",
                type = MessageType.EMAIL,
                status = MessageStatus.QUEUED,
                message = "test",
                sendAfter = Instant.now(),
                to = "<EMAIL>",
                language = Language.SPANISH
            )
        )

        assertThat(actual).isEqualTo("test\n\nSi prefiere no recibir más mensajes de este remitente, siga el enlace y confirme su solicitud. http://localhost:8080/unsubscribe?id=test-id&email=test%40test.com")
    }

    @Test
    fun `initiate should retry using max delay of all failed broadcast destinations`() {
        val message = PmxMessage(
            id = "test-id",
            customerId = "1",
            type = MessageType.EMAIL_BROADCAST,
            status = MessageStatus.QUEUED,
            message = "test",
            sendAfter = Instant.now(),
            emailRecipients = listOf(
                PmxMessage.EmailRecipient(address = "<EMAIL>", status = MessageStatus.QUEUED),
                PmxMessage.EmailRecipient(address = "<EMAIL>", status = MessageStatus.QUEUED),
                PmxMessage.EmailRecipient(address = "<EMAIL>", status = MessageStatus.QUEUED)
            )
        )
        every { pmxMessageService.getById("test-id") } returns message
        every { subscriptionService.getByIds(any()) } returns emptyList()
        every { emailService.sendBulkEmail(any(), any(), captureLambda()) } answers {
            val entryResults = listOf(
                SendBulkEmailResult.EntryResult(
                    success = true,
                    toAddress = "<EMAIL>",
                    retry = false,
                    messageId = "1"
                ),
                SendBulkEmailResult.EntryResult(
                    success = false,
                    toAddress = "<EMAIL>",
                    retry = true,
                    retryDelay = Duration.ofSeconds(1)
                ),
                SendBulkEmailResult.EntryResult(
                    success = false,
                    toAddress = "<EMAIL>",
                    retry = true,
                    retryDelay = Duration.ofHours(1)
                )
            )
            lambda<(List<SendBulkEmailResult.EntryResult>) -> Unit>().captured.invoke(entryResults)
            SendBulkEmailResult(success = false, entryResults = entryResults)
        }
        every { pmxMessageService.patch("test-id", any()) } returns message

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(actual.retryDelay).isEqualTo(Duration.ofHours(1))

        verify {
            pmxMessageService.patch(
                "test-id",
                mapOf("emailRecipients.0.remoteId" to "1", "emailRecipients.0.status" to MessageStatus.DISPATCHED,
                    "emailRecipients.1.remoteId" to null, "emailRecipients.1.status" to MessageStatus.QUEUED,
                    "emailRecipients.2.remoteId" to null, "emailRecipients.2.status" to MessageStatus.QUEUED)
            )
        }
    }

    @Test
    fun `Bulk emails correctly replace plaintext newline with HTML break`() {
        val message = PmxMessage(
            type = MessageType.EMAIL_BROADCAST,
            emailRecipients = mutableListOf(
                PmxMessage.EmailRecipient(
                    address = "<EMAIL>",
                    status = MessageStatus.QUEUED
                )
            ),
            message = "This is a test.\n\nThere should be three paragraphs.\n\nThanks,\nDan.",
            subject = "Test Email",
            sendAfter = Instant.now(),
            customerId = "someId",
            status = MessageStatus.QUEUED,
            remoteId = UUID.randomUUID().toString(),
            id = "someId"
        )
        val customer = Customer(
            name = "Valued Customer",
            status = CustomerStatus.ENABLED,
            id = "someId"
        )
        val expectedTemplate = BulkEmailMessage.Template(
            name = message.id!!,
            subject = message.subject,
            htmlContent = "This is a test.<br><br>There should be three paragraphs.<br><br>Thanks,<br>Dan."
        )
        every { subscriptionService.getByIds(any()) } returns listOf(SubscriptionPreference(
            id = "someId",
            subscribed = true
        ))
        every { emailService.sendBulkEmail(any(), any(), captureLambda()) } answers {
            SendBulkEmailResult(
                success = true,
                entryResults = mutableListOf(
                    SendBulkEmailResult.EntryResult(
                        success = true,
                        toAddress = "<EMAIL>"
                    )
                )
            )
        }

        sut.initiate(message, customer)

        verify {
            emailService.sendBulkEmail(match { message ->
                message.template.htmlContent == expectedTemplate.htmlContent
            }, any(), captureLambda())
        }
    }

    companion object {
        private const val UNSUBSCRIBED_ADDRESS = "<EMAIL>"
    }
}