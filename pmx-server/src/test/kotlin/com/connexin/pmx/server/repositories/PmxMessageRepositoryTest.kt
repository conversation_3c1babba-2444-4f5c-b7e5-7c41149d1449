package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.utils.TestVariation
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageRequest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.findAllAndRemove
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import java.time.*
import java.time.temporal.ChronoUnit

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class PmxMessageRepositoryTest {
    @Autowired
    private lateinit var template: MongoTemplate

    // it is 7 PM on 2/23... that's when UTC clock flips to next day
    private val clock = Clock.fixed(Instant.parse("2022-02-24T00:00:00Z"), ZoneOffset.UTC)

    @Autowired
    private lateinit var sut: PmxMessageRepository

    @BeforeEach
    fun setup() {
    }

    @AfterEach
    fun cleanup() {
        template.findAllAndRemove<PmxMessage>(Query())
    }

    @Test
    fun findQueuedByEngagementId() {
        // good 1: queued
        template.save(
            PmxMessage(
                id = "queued-1",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test queued",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "test"
            )
        )

        // good 2: queued
        template.save(
            PmxMessage(
                id = "queued-2",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test queued",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "test"
            )
        )

        // bad: sent
        template.save(
            PmxMessage(
                id = "failed",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test sent",
                status = MessageStatus.SENT,
                sendAfter = Instant.now(clock),
                engagementId = "test"
            )
        )

        val actual = sut.findQueuedByEngagementId("test")

        assertThat(actual.map { it.id }).containsExactly("queued-1", "queued-2")
    }

    @Test
    fun `findReadyToDispatch should find all queued messages where sendAfter has transpired`() {
        template.save(
            // this message already sent
            PmxMessage(
                id = "sent",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test sent",
                status = MessageStatus.SENT,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS)
            )
        )

        template.save(
            // this message failed
            PmxMessage(
                id = "failed",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test failed",
                status = MessageStatus.FAILED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS)
            )
        )

        template.save(
            // this message dispatched
            PmxMessage(
                id = "dispatched",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test dispatched",
                status = MessageStatus.DISPATCHED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS)
            )
        )

        template.save(
            // this message delivered
            PmxMessage(
                id = "delivered",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS)
            )
        )

        template.save(
            // this message scheduled in the past
            // no send window
            PmxMessage(
                id = "queued_past_no_window",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS)
            )
        )

        template.save(
            // this message scheduled in the past
            // within send window
            PmxMessage(
                id = "queued_past_in_window_est",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS),
                sendWindow = PmxMessage.SendWindow(
                    from = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 9, 0, 0).toInstant(ZoneOffset.of("-5"))
                    ),
                    until = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 21, 0, 0).toInstant(ZoneOffset.of("-5"))
                    )
                )
            )
        )

        template.save(
            // this message scheduled in the past
            // within send window
            PmxMessage(
                id = "queued_past_in_window_pacific",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS),
                sendWindow = PmxMessage.SendWindow(
                    from = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 9, 0, 0).toInstant(ZoneOffset.of("-8"))
                    ),
                    until = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 21, 0, 0).toInstant(ZoneOffset.of("-8"))
                    )
                )
            )
        )

        template.save(
            // this message scheduled in the past
            // out of send window
            PmxMessage(
                id = "queued_past_not_in_window",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock).minus(10, ChronoUnit.DAYS),
                sendWindow = PmxMessage.SendWindow(
                    from = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 5, 0, 0).toInstant(ZoneOffset.of("-5"))
                    ),
                    until = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 6, 0, 0).toInstant(ZoneOffset.of("-5"))
                    )
                )
            )
        )

        template.save(
            // this message scheduled now
            // no send window
            PmxMessage(
                id = "queued_now_no_window",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock)
            )
        )

        template.save(
            // this message scheduled in the future
            // no send window
            PmxMessage(
                id = "queued_future_no_window",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock).plus(10, ChronoUnit.DAYS)
            )
        )

        val actual = sut.findReadyToDispatch(Instant.now(clock), PageRequest.of(0, 10))

        assertThat(actual.map { it.id }).containsExactly("queued_past_no_window", "queued_past_in_window_est","queued_past_in_window_pacific", "queued_now_no_window")
    }

    @Test
    fun `findReadyToDispatch should be safe using sendWindow and start of DST`() {
        val before = LocalDateTime.of(2022, 3, 13, 8, 0)
        val at = LocalDateTime.of(2022, 3, 13, 9, 0)
        val after = LocalDateTime.of(2022, 3, 13, 10, 0)
        template.save(
            // this message scheduled in the future
            // no send window
            PmxMessage(
                id = "dst",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = OffsetDateTime.of(at, ZoneId.of("US/Eastern").rules.getOffset(at)).toInstant(),
                sendWindow = PmxMessage.SendWindow(
                    from = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 9, 0, 0).toInstant(ZoneOffset.of("-4"))
                    ),
                    until = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 21, 0, 0).toInstant(ZoneOffset.of("-4"))
                    )
                )
            )
        )

        val actualBefore = sut.findReadyToDispatch(before.toInstant(ZoneId.of("US/Eastern").rules.getOffset(before)), PageRequest.of(0, 10))
        val actualDuring = sut.findReadyToDispatch(at.toInstant(ZoneId.of("US/Eastern").rules.getOffset(at)), PageRequest.of(0, 10))
        val actualAfter = sut.findReadyToDispatch(after.toInstant(ZoneId.of("US/Eastern").rules.getOffset(after)), PageRequest.of(0, 10))

        assertThat(actualBefore).isEmpty()
        assertThat(actualDuring.map { it.id }).containsExactly("dst")
        assertThat(actualAfter.map { it.id }).containsExactly("dst")
    }

    @Test
    fun `findReadyToDispatch should be safe using sendWindow and end of DST`() {
        val before = LocalDateTime.of(2022, 11, 6, 8, 0)
        val at = LocalDateTime.of(2022, 11, 6, 9, 0)
        val after = LocalDateTime.of(2022, 11, 6, 10, 0)
        template.save(
            // this message scheduled in the future
            // no send window
            PmxMessage(
                id = "dst",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = OffsetDateTime.of(at, ZoneId.of("US/Eastern").rules.getOffset(at)).toInstant(),
                sendWindow = PmxMessage.SendWindow(
                    from = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 9, 0, 0).toInstant(ZoneOffset.of("-5"))
                    ),
                    until = Instant.from(
                        LocalDateTime.of(2022, 1, 1, 21, 0, 0).toInstant(ZoneOffset.of("-5"))
                    )
                )
            )
        )

        val actualBefore = sut.findReadyToDispatch(before.toInstant(ZoneId.of("US/Eastern").rules.getOffset(before)), PageRequest.of(0, 10))
        val actualDuring = sut.findReadyToDispatch(at.toInstant(ZoneId.of("US/Eastern").rules.getOffset(at)), PageRequest.of(0, 10))
        val actualAfter = sut.findReadyToDispatch(after.toInstant(ZoneId.of("US/Eastern").rules.getOffset(after)), PageRequest.of(0, 10))

        assertThat(actualBefore).isEmpty()
        assertThat(actualDuring.map { it.id }).containsExactly("dst")
        assertThat(actualAfter.map { it.id }).containsExactly("dst")
    }

    @Test
    fun `updateFields should update fields and return updated message`() {
        template.save(
            PmxMessage(
                id = "test-id",
                type = MessageType.EMAIL_BROADCAST,
                customerId = "1",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.EPOCH,
                emailRecipients = listOf(
                    PmxMessage.EmailRecipient(
                        address = "<EMAIL>",
                        status = MessageStatus.QUEUED
                    ),
                    PmxMessage.EmailRecipient(
                        address = "<EMAIL>",
                        status = MessageStatus.QUEUED
                    )
                )
            )
        )

        val actual = sut.updateFields(
            "test-id",
            mapOf(
                "status" to MessageStatus.DISPATCHED,
                "message" to "updated",
                "sendAfter" to Instant.EPOCH.plusSeconds(5),
                "emailRecipients.1.status" to MessageStatus.DISPATCHED,
                "emailRecipients.1.remoteId" to "test-remote-id"
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual).isEqualTo(
            PmxMessage(
                id = "test-id",
                type = MessageType.EMAIL_BROADCAST,
                customerId = "1",
                message = "updated",
                status = MessageStatus.DISPATCHED,
                sendAfter = Instant.EPOCH.plusSeconds(5),
                emailRecipients = listOf(
                    PmxMessage.EmailRecipient(
                        address = "<EMAIL>",
                        status = MessageStatus.QUEUED
                    ),
                    PmxMessage.EmailRecipient(
                        address = "<EMAIL>",
                        status = MessageStatus.DISPATCHED,
                        remoteId = "test-remote-id"
                    )
                )
            )
        )
    }

    @Test
    fun findByTypeAndStatusAndTo() {
        // queued
        template.save(
            PmxMessage(
                id = "queued",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test queued",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "engagement-queued"
            )
        )
        // delivered SMS
        template.save(
            PmxMessage(
                id = "delivered-new",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "delivered-old",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock).minusSeconds(30),
                engagementId = "engagement-old"
            )
        )
        template.save(
            PmxMessage(
                id = "delivered-no-engagement",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock).minusSeconds(30)
            )
        )
        // delivered EMAIL
        template.save(
            PmxMessage(
                id = "delivered-email",
                type = MessageType.EMAIL,
                customerId = "1",
                to = "<EMAIL>",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-email"
            )
        )

        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")

        assertThat(actual).isNotNull
        assertThat(actual!!.id).isEqualTo("delivered-new")
    }


    @Test
    fun findMostRecentDeliveredWhenSent() {
        // queued
        template.save(
            PmxMessage(
                id = "queued",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test queued",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "engagement-queued"
            )
        )
        // sent SMS
        template.save(
            PmxMessage(
                id = "sent-new",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.SENT,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "delivered-old",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock).minusSeconds(30),
                engagementId = "engagement-old"
            )
        )
        template.save(
            PmxMessage(
                id = "delivered-no-engagement",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock).minusSeconds(30)
            )
        )
        // delivered EMAIL
        template.save(
            PmxMessage(
                id = "delivered-email",
                type = MessageType.EMAIL,
                customerId = "1",
                to = "<EMAIL>",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-email"
            )
        )

        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")

        assertThat(actual).isNotNull
        assertThat(actual!!.id).isEqualTo("sent-new")
    }

    @Test
    fun givenOnlyValidDeliveredFindMostRecentDeliveredWhenSent() {

        template.save(
            PmxMessage(
                id = "delivered-only-wrong-number",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551255",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )

        template.save(
            PmxMessage(
                id = "delivered-only",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )


        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")

        assertThat(actual).isNotNull
        assertThat(actual!!.id).isEqualTo("delivered-only")
    }

    @Test
    fun givenOnlySentFindMostRecentDeliveredWhenSent() {
        template.save(
            PmxMessage(
                id = "sent-only",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.SENT,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )


        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")

        assertThat(actual).isNotNull
        assertThat(actual!!.id).isEqualTo("sent-only")
    }

    @Test
    fun givenNoValidSentOrDeliveredReturnNull() {
        template.save(
            PmxMessage(
                id = "sent-only-wrong-number",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405555555",
                message = "test delivered",
                status = MessageStatus.SENT,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "delivered-only-wrong-number",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405556789",
                message = "test delivered",
                status = MessageStatus.DELIVERED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "queued",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "dispatched",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.DISPATCHED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )
        template.save(
            PmxMessage(
                id = "failed",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test delivered",
                status = MessageStatus.FAILED,
                sendAfter = Instant.now(clock),
                completedAt = Instant.now(clock),
                engagementId = "engagement-new"
            )
        )


        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")

        assertThat(actual).isNull()
    }

    @Test
    fun getMostRecentByEngagementIdAndContact() {
        template.save(
            PmxMessage(
                id = "message-1",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test 1",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "engagement-1",
                completedAt = Instant.now().minusSeconds(1)
            )
        )
        template.save(
            PmxMessage(
                id = "message-2",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test 2",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "engagement-1",
                completedAt = Instant.now()
            )
        )

        val actual = sut.getMostRecentByEngagementIdAndContact(
            "engagement-1",
            "+14405551234"
        )

        assertThat(actual).isNotNull
        assertThat(actual!!.id).isEqualTo("message-2")
    }

    @Test
    fun `getMostRecentByEngagementIdAndContact returns null`() {
        template.save(
            PmxMessage(
                id = "message-2",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test 2",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now(clock),
                engagementId = "engagement-1",
                completedAt = Instant.now()
            )
        )

        val actual = sut.getMostRecentByEngagementIdAndContact(
            "engagement-2",
            "+14405551234"
        )

        assertThat(actual).isNull()
    }


}