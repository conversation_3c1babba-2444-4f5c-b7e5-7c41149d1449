package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.TestFileUtil
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.BeforeRespondResult
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.TelnyxErrorCodes
import com.fasterxml.jackson.databind.cfg.CoercionAction
import com.fasterxml.jackson.databind.cfg.CoercionInputShape
import com.fasterxml.jackson.databind.type.LogicalType
import com.telnyx.sdk.model.Error
import com.telnyx.sdk.model.ErrorMeta
import io.micrometer.core.instrument.Timer
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockkClass
import io.mockk.verifySequence
import net.javacrumbs.shedlock.core.LockAssert
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.data.domain.PageRequest
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@ExtendWith(MockKExtension::class)
class MessageDispatcherFacadeImplTest {
    @MockK
    lateinit var voiceMessageDispatcher: VoiceMessageDispatcher

    @MockK
    lateinit var smsMessageDispatcher: SmsMessageDispatcher

    @MockK
    lateinit var emailMessageDispatcher: EmailMessageDispatcher

    @MockK
    lateinit var messageService: PmxMessageService

    @MockK
    lateinit var customerService: CustomerService

    @MockK
    lateinit var meterService: MeterService

    @MockK
    lateinit var engagementService: EngagementService

    @RelaxedMockK
    lateinit var responseHandler: EngagementResponseHandler

    private lateinit var sut: MessageDispatcherFacadeImpl

    private val customer = Customer(
        id = "1",
        status = CustomerStatus.ENABLED,
        name = "Test",
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateIds = mapOf(TemplateScenario.DEFAULT to "test")
            )
        )
    )


    @BeforeEach
    fun setup() {
        LockAssert.TestHelper.makeAllAssertsPass(true)
        TestFileUtil.mapper.coercionConfigFor(LogicalType.Integer).setCoercion(CoercionInputShape.String, CoercionAction.TryConvert)
        sut = MessageDispatcherFacadeImpl(
            voiceMessageDispatcher,
            smsMessageDispatcher,
            emailMessageDispatcher,
            messageService,
            customerService,
            10,
            meterService,
            engagementService,
            responseHandler,
            TestFileUtil.mapper,
            4,
            250
        )

        every { meterService.startTimer() } returns Timer.start()
        every { meterService.getDispatchScheduledTimer() } returns mockkClass(Timer::class, relaxed = true)
        every { meterService.getDispatchInitiateTimer(any(), any(), any()) } returns mockkClass(
            Timer::class,
            relaxed = true
        )
        every { meterService.getDispatchRespondTimer(any(), any(), any()) } returns mockkClass(
            Timer::class,
            relaxed = true
        )
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `initiate should return non-success if customer could not be found`() {
        every { customerService.getById("1") } returns null
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.QUEUED,
            type = MessageType.SMS,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.initiate(message, emptyMap())

        assertThat(actual.success).isFalse
    }

    @ParameterizedTest(name = "given the message type {0}, initiate should record a message response regarding a failure if associated with an engagement")
    @MethodSource("messageTypes")
    fun `initiate should record a message response regarding a failure if associated with an engagement`(type: MessageType) {
        val engagement = Engagement(
            id = "engagement1",
            status = EngagementStatus.CONFIRM,
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                ContactResource(
                    id = "1",
                    contactMethod = when(type) {
                        MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> ContactMethod.EMAIL
                        MessageType.SMS -> ContactMethod.SMS
                        MessageType.VOICE -> ContactMethod.VOICE
                    },
                    phone = "+14405551234",
                    email = "+14405551234",
                    language = Language.ENGLISH,
                    familyName = "test",
                    givenName = "test"
                )
            )
        )

        val message = PmxMessage(
            id = "test-id",
            customerId = customer.id!!,
            status = MessageStatus.QUEUED,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementRuleId = "1",
            engagementId = engagement.id,
            altMessage = "alt test",
            subject = "subject test"
        )
        every { customerService.getById(message.customerId) } returns customer
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = false,
                    remoteId = "test"
                )
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = false,
                    remoteId = "test"
                )
            }
            else -> every { emailMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                success = false,
                remoteId = "test"
            )
        }
        every { messageService.patch("test-id", any()) } returns message
        every { responseHandler.recordMessageResponse(any(), any(), any(), any(), any(), any(), any(), any(), any()) } answers {
            MessageResponse(
                id = "test",
                customerId = customer.id!!,
                engagementId = engagement.id!!,
                appointments = emptySet(),
                respondents = emptySet(),
                workflow = EngagementWorkflow.CONFIRMATION,
                status = arg(3),
                messageId = arg(4),
                errors = arg(5),
                message = arg(6),
                altMessage = arg(7),
                subject = arg(8)
            )
        }

        val actual = sut.initiate(message, mapOf(engagement.id!! to engagement))

        assertThat(actual.success).isFalse

        verifySequence {
            customerService.getById("1")

            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.initiate(message, customer)
                MessageType.SMS -> smsMessageDispatcher.initiate(message, customer)
                else -> emailMessageDispatcher.initiate(message, customer)
            }

            responseHandler.recordMessageResponse(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                engagement.resources.filterIsInstance<ContactResource>().first(),
                MessageStatus.FAILED,
                message.id!!,
                any(),
                message.message,
                message.altMessage,
                message.subject
            )

            messageService.patch("test-id", mapOf("status" to MessageStatus.FAILED, "attempts" to 1, "errors" to null))
        }
    }

    @ParameterizedTest(name = "given the message type {0}, initiate should record a message response regarding a success if associated with an engagement")
    @MethodSource("messageTypes")
    fun `initiate should record a message response regarding a success if associated with an engagement`(type: MessageType) {
        val engagement = Engagement(
            id = "engagement1",
            status = EngagementStatus.CONFIRM,
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                ContactResource(
                    id = "1",
                    contactMethod = when(type) {
                        MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> ContactMethod.EMAIL
                        MessageType.SMS -> ContactMethod.SMS
                        MessageType.VOICE -> ContactMethod.VOICE
                    },
                    phone = "+14405551234",
                    email = "+14405551234",
                    language = Language.ENGLISH,
                    familyName = "test",
                    givenName = "test"
                )
            )
        )

        val message = PmxMessage(
            id = "test-id",
            customerId = customer.id!!,
            status = MessageStatus.QUEUED,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementRuleId = "1",
            engagementId = engagement.id,
            altMessage = "alt test",
            subject = "subject test"
        )
        every { customerService.getById(message.customerId) } returns customer
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            else -> every { emailMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                success = true,
                remoteId = "test"
            )
        }
        every { messageService.patch("test-id", any()) } returns message
        every { responseHandler.recordMessageResponse(any(), any(), any(), any(), any(), any(), any(), any(), any()) } answers {
            MessageResponse(
                id = "test",
                customerId = customer.id!!,
                engagementId = engagement.id!!,
                appointments = emptySet(),
                respondents = emptySet(),
                workflow = EngagementWorkflow.CONFIRMATION,
                status = arg(3),
                messageId = arg(4),
                errors = arg(5),
                message = arg(6),
                altMessage = arg(7),
                subject = arg(8)
            )
        }

        val actual = sut.initiate(message, mapOf(engagement.id!! to engagement))

        assertThat(actual.success).isTrue

        verifySequence {
            customerService.getById("1")

            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.initiate(message, customer)
                MessageType.SMS -> smsMessageDispatcher.initiate(message, customer)
                else -> emailMessageDispatcher.initiate(message, customer)
            }

            responseHandler.recordMessageResponse(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                engagement.resources.filterIsInstance<ContactResource>().first(),
                MessageStatus.DISPATCHED,
                message.id!!,
                any(),
                message.message,
                message.altMessage,
                message.subject
            )

            messageService.patch("test-id", mapOf("remoteId" to "test", "status" to MessageStatus.DISPATCHED, "attempts" to 1))
        }
    }

    @ParameterizedTest(name = "given the message type {0}, initiate should dispatch the message and set the status to dispatched")
    @MethodSource("messageTypes")
    fun `initiate should dispatch the message and set the status to DISPATCHED`(type: MessageType) {
        val message = PmxMessage(
            id = "test-id",
            customerId = "1",
            status = MessageStatus.QUEUED,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH
        )
        val customer = Customer(
            id = "1", name = "Test", organizationId = 1L,
            status = CustomerStatus.ENABLED
        )

        every { customerService.getById("1") } returns customer
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            else -> every { emailMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                success = true,
                remoteId = "test"
            )
        }
        every { messageService.patch("test-id", any()) } returns message

        val actual = sut.initiate(message, emptyMap())

        assertThat(actual.success).isTrue
        assertThat(actual.remoteId).isEqualTo("test")
        assertThat(message.status).isEqualTo(MessageStatus.DISPATCHED)
        assertThat(message.remoteId).isEqualTo("test")

        verifySequence {
            customerService.getById("1")

            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.initiate(message, customer)
                MessageType.SMS -> smsMessageDispatcher.initiate(message, customer)
                else -> emailMessageDispatcher.initiate(message, customer)
            }

            messageService.patch("test-id", mapOf("remoteId" to "test", "status" to MessageStatus.DISPATCHED, "attempts" to 1))
        }
    }



    @ParameterizedTest(name = "given the message type {0}, initiate should dispatch the message and set the status to dispatched even with contact not found in engagement")
    @MethodSource("messageTypes")
    fun `initiate should dispatch the message and set the status to DISPATCHED even with contact not found on engagement`(type: MessageType) {
        val message = PmxMessage(
            id = "test-id",
            customerId = "1",
            status = MessageStatus.QUEUED,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementId = "123",
            engagementRuleId = "12345"
        )
        val customer = Customer(
            id = "1", name = "Test", organizationId = 1L,
            status = CustomerStatus.ENABLED,
            engagementRules = mutableSetOf(EngagementRule("12345", true, EngagementWorkflow.CONFIRMATION))
        )

        every { customerService.getById("1") } returns customer
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                    success = true,
                    remoteId = "test"
                )
            }
            else -> every { emailMessageDispatcher.initiate(message, customer) } returns InitiateResult(
                success = true,
                remoteId = "test"
            )
        }
        every { messageService.patch("test-id", any()) } returns message

        val engagement = Engagement("123", message.customerId, null, null, EngagementStatus.CONFIRM,
            Instant.now(), Instant.now())

        val actual = sut.initiate(message, mapOf(engagement.id!! to engagement))

        assertThat(actual.success).isTrue
        assertThat(actual.remoteId).isEqualTo("test")
        assertThat(message.status).isEqualTo(MessageStatus.DISPATCHED)
        assertThat(message.remoteId).isEqualTo("test")

        verifySequence {
            customerService.getById("1")

            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.initiate(message, customer)
                MessageType.SMS -> smsMessageDispatcher.initiate(message, customer)
                else -> emailMessageDispatcher.initiate(message, customer)
            }

            messageService.patch("test-id", mapOf("remoteId" to "test", "status" to MessageStatus.DISPATCHED, "attempts" to 1))
        }
    }

    @ParameterizedTest(name = "given the message type {0}, respond should fail if dispatcher cannot determine the remoteId")
    @MethodSource("messageTypes")
    fun `respond should fail if dispatcher does not accept on beforeRespond`(type: MessageType) {
        val beforeRespond = BeforeRespondResult(success = true, accepted = false, context = RespondContext(payload = "test", type = type))
        when (type) {
            MessageType.SMS -> every { smsMessageDispatcher.beforeRespond(any()) } returns beforeRespond
            MessageType.VOICE -> every { voiceMessageDispatcher.beforeRespond(any()) } returns beforeRespond
            else -> every { emailMessageDispatcher.beforeRespond(any()) } returns beforeRespond
        }

        val actual = sut.respond(type, "test")

        assertThat(actual.success).isFalse

        verifySequence {
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.beforeRespond(any())
                MessageType.SMS -> smsMessageDispatcher.beforeRespond(any())
                else -> emailMessageDispatcher.beforeRespond(any())
            }
        }
    }

    @ParameterizedTest(name = "given the message type {0}, respond should fail if message matching remoteId could not be found")
    @MethodSource("messageTypes")
    fun `respond should fail if message matching remoteId could not be found`(type: MessageType) {
        val beforeRespond = BeforeRespondResult(success = true, accepted = true, context = RespondContext(payload = "test", type = type, remoteId = "test"))
        when (type) {
            MessageType.SMS -> every { smsMessageDispatcher.beforeRespond(any()) } returns beforeRespond
            MessageType.VOICE -> every { voiceMessageDispatcher.beforeRespond(any()) } returns beforeRespond
            else -> every { emailMessageDispatcher.beforeRespond(any()) } returns beforeRespond
        }
        every { messageService.getByRemoteId("test") } returns null

        val actual = sut.respond(type, "test")

        assertThat(actual.success).isFalse

        verifySequence {
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.beforeRespond(any())
                MessageType.SMS -> smsMessageDispatcher.beforeRespond(any())
                else -> emailMessageDispatcher.beforeRespond(any())
            }
            messageService.getByRemoteId("test")
        }
    }

    @ParameterizedTest(name = "given the message type {0}, respond should dispatch response and update message")
    @MethodSource("messageTypes")
    fun `respond should dispatch response and update message`(type: MessageType) {
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.QUEUED,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH
        )
        val customer = Customer(id = "1", status = CustomerStatus.ENABLED, name = "Test")

        every { customerService.getById(message.customerId) } returns customer

        val beforeRespond = BeforeRespondResult(success = true, accepted = true, context = RespondContext(payload = "test", type = type, remoteId = "test"))
        val context = beforeRespond.context.copy(message = message)
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { smsMessageDispatcher.respond(context) } returns RespondResult(success = true)
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { voiceMessageDispatcher.respond(context) } returns RespondResult(success = true)
            }
            else -> {
                every { emailMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { emailMessageDispatcher.respond(context) } returns RespondResult(success = true)
            }
        }
        every { messageService.getByRemoteId("test") } returns message
        every { messageService.update(message) } returns message

        val actual = sut.respond(type, "test")

        assertThat(actual.success).isTrue

        verifySequence {
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.beforeRespond(any())
                MessageType.SMS -> smsMessageDispatcher.beforeRespond(any())
                else -> emailMessageDispatcher.beforeRespond(any())
            }
            messageService.getByRemoteId("test")
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.respond(context)
                MessageType.SMS -> smsMessageDispatcher.respond(context)
                else -> emailMessageDispatcher.respond(context)
            }
            messageService.update(message)
        }
    }

    @ParameterizedTest(name = "given the message type {0}, respond should dispatch response record an engagement response and update message")
    @MethodSource("messageTypes")
    fun `respond should dispatch response record an engagement response and update message`(type: MessageType) {
        val engagement = Engagement(
            id = "engagement1",
            status = EngagementStatus.CONFIRM,
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                ContactResource(
                    id = "1",
                    contactMethod = when(type) {
                        MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> ContactMethod.EMAIL
                        MessageType.SMS -> ContactMethod.SMS
                        MessageType.VOICE -> ContactMethod.VOICE
                    },
                    phone = "+14405551234",
                    email = "+14405551234",
                    language = Language.ENGLISH,
                    familyName = "test",
                    givenName = "test"
                )
            )
        )
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.SENT,
            type = type,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementId = engagement.id,
            engagementRuleId = customer.engagementRules.first().id,
            altMessage = "alt test",
            subject = "subject test"
        )

        every { customerService.getById(message.customerId) } returns customer
        every { engagementService.getById(engagement.id!!) } returns engagement
        every { responseHandler.recordMessageResponse(any(), any(), any(), any(), any(), any(), any(), any(), any()) } answers {
            MessageResponse(
                id = "test",
                customerId = customer.id!!,
                engagementId = engagement.id!!,
                appointments = emptySet(),
                respondents = emptySet(),
                workflow = EngagementWorkflow.CONFIRMATION,
                status = arg(3),
                messageId = arg(4),
                errors = arg(5),
                message = arg(6),
                altMessage = arg(7),
                subject = arg(8)
            )
        }

        val beforeRespond = BeforeRespondResult(success = true, accepted = true, context = RespondContext(payload = "test", type = type, remoteId = "test"))
        val context = beforeRespond.context.copy(message = message)
        when (type) {
            MessageType.SMS -> {
                every { smsMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { smsMessageDispatcher.respond(context) } answers {
                    firstArg<RespondContext>().message!!.status = MessageStatus.DELIVERED
                    RespondResult(success = true)
                }
            }
            MessageType.VOICE -> {
                every { voiceMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { voiceMessageDispatcher.respond(context) } answers {
                    firstArg<RespondContext>().message!!.status = MessageStatus.DELIVERED
                    RespondResult(success = true)
                }
            }
            else -> {
                every { emailMessageDispatcher.beforeRespond(any()) } returns beforeRespond
                every { emailMessageDispatcher.respond(context) } answers {
                    firstArg<RespondContext>().message!!.status = MessageStatus.DELIVERED
                    RespondResult(success = true)
                }
            }
        }
        every { messageService.getByRemoteId("test") } returns message
        every { messageService.update(message) } returns message

        val actual = sut.respond(type, "test")

        assertThat(actual.success).isTrue

        verifySequence {
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.beforeRespond(any())
                MessageType.SMS -> smsMessageDispatcher.beforeRespond(any())
                else -> emailMessageDispatcher.beforeRespond(any())
            }
            messageService.getByRemoteId("test")
            when (type) {
                MessageType.VOICE -> voiceMessageDispatcher.respond(context)
                MessageType.SMS -> smsMessageDispatcher.respond(context)
                else -> emailMessageDispatcher.respond(context)
            }
            customerService.getById(message.customerId)
            engagementService.getById(engagement.id!!)
            responseHandler.recordMessageResponse(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                engagement.resources.filterIsInstance<ContactResource>().first(),
                MessageStatus.DELIVERED,
                message.id,
                any(),
                message.message,
                message.altMessage,
                message.subject
            )
            messageService.update(message)
        }
    }

    @ParameterizedTest
    @MethodSource("confirmationStatuses")
    fun `respond should record confirmation response when a voice message completes`(expectedStatus: ConfirmationStatus) {
        val engagement = Engagement(
            id = "engagement1",
            status = EngagementStatus.CONFIRM,
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                ContactResource(
                    id = "1",
                    contactMethod = ContactMethod.VOICE,
                    phone = "+14405551234",
                    email = "+14405551234",
                    language = Language.ENGLISH,
                    familyName = "test",
                    givenName = "test"
                )
            )
        )
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.DISPATCHED,
            confirmationStatus = expectedStatus,
            type = MessageType.VOICE,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementId = engagement.id,
            engagementRuleId = customer.engagementRules.first().id,
            altMessage = "alt test",
            subject = "subject test"
        )

        every { customerService.getById(message.customerId) } returns customer
        every { engagementService.getById(engagement.id!!) } returns engagement
        every { engagementService.sendEvent(any<ConfirmationResponseEvent>()) } answers {
            firstArg<ConfirmationResponseEvent>().engagement
        }
        every { responseHandler.recordMessageResponse(any(), any(), any(), any(), any(), any(), any(), any(), any()) } answers {
            MessageResponse(
                id = "test",
                customerId = customer.id!!,
                engagementId = engagement.id!!,
                appointments = emptySet(),
                respondents = emptySet(),
                workflow = EngagementWorkflow.CONFIRMATION,
                status = arg(3),
                messageId = arg(4),
                errors = arg(5),
                message = arg(6),
                altMessage = arg(7),
                subject = arg(8)
            )
        }

        val beforeRespond = BeforeRespondResult(success = true, accepted = true, context = RespondContext(payload = "test", type = MessageType.VOICE, remoteId = "test"))
        val context = beforeRespond.context.copy(message = message)
        every { voiceMessageDispatcher.beforeRespond(any()) } returns beforeRespond
        every { voiceMessageDispatcher.respond(context) } answers {
            firstArg<RespondContext>().message!!.status = MessageStatus.DELIVERED
            RespondResult(success = true)
        }
        every { messageService.getByRemoteId("test") } returns message
        every { messageService.update(message) } returns message

        val actual = sut.respond(MessageType.VOICE, "test")

        assertThat(actual.success).isTrue

        verifySequence {
            voiceMessageDispatcher.beforeRespond(any())
            messageService.getByRemoteId("test")
            voiceMessageDispatcher.respond(context)
            customerService.getById(message.customerId)
            engagementService.getById(engagement.id!!)
            responseHandler.recordMessageResponse(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                engagement.resources.filterIsInstance<ContactResource>().first(),
                MessageStatus.DELIVERED,
                message.id,
                any(),
                message.message,
                message.altMessage,
                message.subject
            )
            if (expectedStatus != ConfirmationStatus.NO_RESPONSE) {
                engagementService.sendEvent(match<ConfirmationResponseEvent> {
                    it.event == when (expectedStatus) {
                        ConfirmationStatus.CONFIRMED -> EngagementEvent.CONFIRMED
                        ConfirmationStatus.DECLINED -> EngagementEvent.DECLINED
                        else -> throw IllegalArgumentException()
                    }
                })
            }
            messageService.update(message)
        }
    }

    @ParameterizedTest
    @MethodSource("confirmationStatuses")
    fun `respond should record confirmation response when a sms message completes`(expectedStatus: ConfirmationStatus) {
        val engagement = Engagement(
            id = "engagement1",
            status = EngagementStatus.CONFIRM,
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                ContactResource(
                    id = "1",
                    contactMethod = ContactMethod.SMS,
                    phone = "+14405551234",
                    email = "+14405551234",
                    language = Language.ENGLISH,
                    familyName = "test",
                    givenName = "test"
                )
            )
        )
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.DELIVERED,
            confirmationStatus = expectedStatus,
            type = MessageType.SMS,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            engagementId = engagement.id,
            engagementRuleId = customer.engagementRules.first().id
        )

        every { customerService.getById(message.customerId) } returns customer
        every { engagementService.getById(engagement.id!!) } returns engagement
        every { engagementService.sendEvent(any<ConfirmationResponseEvent>()) } answers {
            firstArg<ConfirmationResponseEvent>().engagement
        }
        every { responseHandler.recordMessageResponse(any(), any(), any(), any(), any(), any(), any(), any(), any()) } answers {
            MessageResponse(
                id = "test",
                customerId = customer.id!!,
                engagementId = engagement.id!!,
                appointments = emptySet(),
                respondents = emptySet(),
                workflow = EngagementWorkflow.CONFIRMATION,
                status = arg(3),
                messageId = arg(4),
                errors = arg(5),
                message = arg(6),
                altMessage = arg(7),
                subject = arg(8)
            )
        }

        val beforeRespond = BeforeRespondResult(
            success = true,
            accepted = true,
            context = RespondContext(payload = "test", type = MessageType.SMS, receivedMessage = RespondContext.ReceivedMessage("+14405551234", "test"))
        )
        val context = beforeRespond.context.copy(message = message)
        every { smsMessageDispatcher.beforeRespond(any()) } returns beforeRespond
        every { smsMessageDispatcher.respond(context) } returns RespondResult(success = true)
        every { messageService.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234") } returns message
        every { messageService.update(message) } returns message

        val actual = sut.respond(MessageType.SMS, "test")

        assertThat(actual.success).isTrue

        verifySequence {
            smsMessageDispatcher.beforeRespond(any())
            messageService.getMostRecentlyDeliveredEngagementSmsMessage("+14405551234")
            smsMessageDispatcher.respond(context)
            customerService.getById(message.customerId)
            engagementService.getById(engagement.id!!)
            if (expectedStatus != ConfirmationStatus.NO_RESPONSE) {
                engagementService.sendEvent(match<ConfirmationResponseEvent> {
                    it.event == when (expectedStatus) {
                        ConfirmationStatus.CONFIRMED -> EngagementEvent.CONFIRMED
                        ConfirmationStatus.DECLINED -> EngagementEvent.DECLINED
                        else -> throw IllegalArgumentException()
                    }
                })
            }
            messageService.update(message)
        }
    }

    @Test
    fun `scheduledDispatch should find and dispatch queued messages`() {
        val customer = Customer(
            id = "1", organizationId = 1L, name = "test",
            status = CustomerStatus.ENABLED
        )
        every { messageService.findReadyToDispatch(any(), any()) } returns listOf(
            PmxMessage(
                id = "test",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now().minus(10, ChronoUnit.DAYS)
            ),
            PmxMessage(
                id = "test2",
                type = MessageType.SMS,
                customerId = "1",
                to = "+14405551234",
                message = "test",
                status = MessageStatus.QUEUED,
                sendAfter = Instant.now().minus(10, ChronoUnit.DAYS)
            )
        )
        every { smsMessageDispatcher.initiate(any(), any()) } returns InitiateResult(success = true, remoteId = "test")
        every { customerService.getById("1") } returns customer
        every { messageService.patch(any(), any()) } returns PmxMessage(id = "test", customerId = "1",
            status = MessageStatus.DISPATCHED, type = MessageType.SMS, to = "+14405551234", message = "test", sendAfter = Instant.now().minus(10, ChronoUnit.DAYS))

        sut.scheduledDispatch()

        verifySequence {
            messageService.findReadyToDispatch(any(), PageRequest.of(0, 10))
            customerService.getById("1")
            smsMessageDispatcher.initiate(any(), customer)
            messageService.patch("test", mapOf("remoteId" to "test", "status" to MessageStatus.DISPATCHED, "attempts" to 1))
            customerService.getById("1")
            smsMessageDispatcher.initiate(any(), customer)
            messageService.patch("test2", mapOf("remoteId" to "test", "status" to MessageStatus.DISPATCHED, "attempts" to 1))
        }
    }

    @Test
    fun `initiate should retry a failed message after the specified delay`() {
        val customer = Customer(
            id = "1", name = "Test", organizationId = 1L,
            status = CustomerStatus.ENABLED
        )
        every { customerService.getById("1") } returns customer
        val now = Instant.now()
        val expectedDelay = Duration.ofMinutes(5)
        val message = PmxMessage(
            id = "test-id",
            customerId = "1",
            status = MessageStatus.QUEUED,
            type = MessageType.SMS,
            message = "test",
            to = "+14405551234",
            sendAfter = now
        )
        every { smsMessageDispatcher.initiate(message, customer) } returns InitiateResult(
            success = false,
            retry = true,
            retryDelay = expectedDelay
        )
        every { messageService.patch("test-id", any()) } returns message

        val actual = sut.initiate(message, emptyMap())

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(message.status).isEqualTo(MessageStatus.QUEUED)
        assertThat(message.sendAfter).isEqualTo(now.plus(expectedDelay))

        verifySequence {
            customerService.getById("1")
            smsMessageDispatcher.initiate(any(), customer)
            messageService.patch("test-id", mapOf("sendAfter" to message.sendAfter, "attempts" to 1))
        }
    }


    @ParameterizedTest(name = "given the telnyx error code {0}, mapPhoneErrorsToDtos should map Telnyx errors to PMX code {1}")
    @MethodSource("telnyxErrorCodes")
    fun `mapPhoneErrorsToDtos should map Telnyx errors to PMX error codes`(telnyxCode: Int, pmxError: Errors) {
        val error = Error()
        error.code = telnyxCode
        error.title = "title"
        error.detail = "detail"

        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.FAILED,
            type = MessageType.SMS,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            errors = TestFileUtil.mapper.writeValueAsString(listOf(error))
        )

        val actual = sut.mapPhoneErrorsToDtos(message)

        assertThat(actual.size).isEqualTo(1)
        assertThat(actual[0].errorCode).isEqualTo(pmxError.code)
        assertThat(actual[0].message).isEqualTo(pmxError.message)
        assertThat(actual[0].details).isNotEmpty
    }

    @ParameterizedTest(name = "given the mail delivery failure reason {0}, mapEmailErrorsToDtos should map reason to PMX code {1}")
    @MethodSource("deliveryFailureReasons")
    fun `mapPhoneErrorsToDtos should map reason to PMX code`(reason: EmailDeliveryFailureReason, error: Errors) {
        val message = PmxMessage(
            customerId = "1",
            status = MessageStatus.FAILED,
            type = MessageType.SMS,
            message = "test",
            to = "+14405551234",
            sendAfter = Instant.EPOCH,
            errors = "test",
            emailDeliveryFailureReason = reason
        )

        val actual = sut.mapEmailErrorsToDtos(message)

        assertThat(actual.size).isEqualTo(1)
        assertThat(actual[0].errorCode).isEqualTo(error.code)
        assertThat(actual[0].message).isEqualTo(error.message)
        assertThat(actual[0].details).isNotEmpty
    }

    companion object {
        @JvmStatic
        fun messageTypes(): Stream<Arguments> = Stream.of(
            Arguments.of(MessageType.VOICE),
            Arguments.of(MessageType.SMS),
            Arguments.of(MessageType.EMAIL),
            Arguments.of(MessageType.EMAIL_BROADCAST),
        )

        @JvmStatic
        fun telnyxErrorCodes(): Stream<Arguments> = Stream.of(
            Arguments.of(TelnyxErrorCodes.BLOCKED_AS_SPAM_INTERNAL, Errors.BLOCKED_SPAM_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.BLOCKED_AS_SPAM_TEMPORARY, Errors.BLOCKED_SPAM_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.BLOCKED_DUE_TO_CONTENT, Errors.BLOCKED_SPAM_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.TOO_MANY_REQUESTS, Errors.BLOCKED_SPAM_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.BLOCKED_AS_SPAM_PERMANENT, Errors.BLOCKED_SPAM_PERMANENT),
            Arguments.of(TelnyxErrorCodes.BLOCKED_DUE_TO_STOP_MESSAGE, Errors.BLOCKED_OPTED_OUT),
            Arguments.of(TelnyxErrorCodes.INVALID_PHONE_NUMBER, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(TelnyxErrorCodes.INVALID_PHONE_NUMBER_FORMAT, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(TelnyxErrorCodes.NOT_ROUTABLE, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(TelnyxErrorCodes.INVALID_DESTINATION_NUMBER, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(TelnyxErrorCodes.INVALID_TO_ADDRESS, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(TelnyxErrorCodes.INACTIVE_PHONE_NUMBER, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.INVALID_SOURCE_NUMBER, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.MESSAGING_PROFILE_DISABLED, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.NO_NUMBERS_IN_POOL, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.TOLL_FREE_NUMBER_NOT_VERIFIED, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.FROM_ADDRESS_TEMPORARILY_UNUSABLE, Errors.CONFIGURATION_ERROR),
            Arguments.of(TelnyxErrorCodes.INVALID_MESSAGE_BODY, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(TelnyxErrorCodes.NO_CONTENT, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(TelnyxErrorCodes.SMS_EXCEEDS_RECOMMENDED_SIZE, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(TelnyxErrorCodes.MESSAGE_EXPIRED_DURING_TRANSMISSION, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.DESTINATION_UNAVAILABLE, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.MESSAGE_EXPIRED_IN_QUEUE, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.UNHEALTHY_FROM_ADDRESS, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.QUEUE_FULL, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(TelnyxErrorCodes.UNDELIVERABLE, Errors.DELIVERY_FAILURE),
        )

        @JvmStatic
        fun deliveryFailureReasons(): Stream<Arguments> = Stream.of(
            Arguments.of(EmailDeliveryFailureReason.ATTACHMENT_REJECTED, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(EmailDeliveryFailureReason.CONTENT_REJECTED, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(EmailDeliveryFailureReason.MESSAGE_TOO_LARGE, Errors.INVALID_MESSAGE_CONTENT),
            Arguments.of(EmailDeliveryFailureReason.SPAM_DETECTED, Errors.BLOCKED_SPAM_TEMPORARY),
            Arguments.of(EmailDeliveryFailureReason.SENDING_IP_BLOCKED, Errors.BLOCKED_SPAM_PERMANENT),
            Arguments.of(EmailDeliveryFailureReason.MAILBOX_FULL, Errors.MAILBOX_FULL),
            Arguments.of(EmailDeliveryFailureReason.UNSUBSCRIBED, Errors.BLOCKED_OPTED_OUT),
            Arguments.of(EmailDeliveryFailureReason.COMMUNICATION_FAILURE, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(EmailDeliveryFailureReason.RECIPIENT_SERVER_ERROR, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(EmailDeliveryFailureReason.VENDOR_INTERNAL_ERROR, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(EmailDeliveryFailureReason.SOFT_BOUNCE, Errors.DISPATCH_FAILURE_TEMPORARY),
            Arguments.of(EmailDeliveryFailureReason.HARD_BOUNCE, Errors.INVALID_EMAIL_OR_PHONE),
            Arguments.of(EmailDeliveryFailureReason.UNDETERMINED, Errors.DELIVERY_FAILURE),

        )

        @JvmStatic
        fun confirmationStatuses(): Stream<Arguments> = Stream.of(
            Arguments.of(ConfirmationStatus.CONFIRMED),
            Arguments.of(ConfirmationStatus.DECLINED),
            Arguments.of(ConfirmationStatus.NO_RESPONSE)
        )
    }
}