package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.TemplateDto
import com.connexin.pmx.server.services.TemplateService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v2/admin/templates")
@Tag(
    name = "Template Admin", description = "Endpoints for administrators to manage templates"
)
class AdminTemplateController(
    private val templateService: TemplateService
) {
    @Operation(
        summary = "Gets a paginated list of templates.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A page of templates."
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority('admin.templates:read')")
    fun find(
        pageable: Pageable
    ): Page<TemplateDto> {
        return templateService.findAll(pageable).map { TemplateDto.from(it) }
    }

    @Operation(
        summary = "Gets a template by ID.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The template record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Template not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.templates:read')")
    fun getById(
        @PathVariable
        id: String
    ): TemplateDto {
        val template = templateService.getById(id)
            ?: throw NotFoundException(errors = listOf(ErrorDto(
                path = "id",
                message = "Not found",
                errorCode = Errors.NOT_FOUND.code
            )))

        return TemplateDto.from(template)
    }

    @GetMapping("/{id}/preview")
    fun preview(
        @PathVariable
        id: String
    ): Map<MessageType, Map<Language, MessageSegments?>> {
        return templateService.preview(id)
            ?: throw NotFoundException(
                errors = listOf(
                    ErrorDto(
                        path = "id",
                        message = "Not found",
                        errorCode = Errors.NOT_FOUND.code
                    )
                )
            )
    }
}