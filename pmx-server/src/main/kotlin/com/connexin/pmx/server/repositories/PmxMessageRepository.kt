package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.MessageStatistic
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.PmxMessage
import com.connexin.pmx.server.utils.InstantUtil
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.FindAndModifyOptions
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.repository.Aggregation
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant
import java.time.ZoneId
import java.util.*

interface PmxMessageRepository : MongoRepository<PmxMessage, String>,
    CustomizedPmxMessageRepository<PmxMessage, String> {
    fun findByRemoteId(remoteId: String): Optional<PmxMessage>

    @Aggregation(
        pipeline = [
            "{ '\$match': { 'type': { '\$in':  ?3}, 'status': {'\$in': ?2 }, 'sendAfter': { '\$gte': ?0, '\$lt': ?1 }  } }",
            "{ '\$group': { '_id': { 'engagement': { '\$gt': ['\$engagementId', null] }, 'type': '\$type', 'status': '\$status' }, 'count': { '\$sum': 1 } } }",
            "{ '\$project': { '_id': 0, 'engagement': '\$_id.engagement', 'type': '\$_id.type', 'status': '\$_id.status', 'count': '\$count' } }"
        ]
    )
    fun getMessageStatistics(from: Instant, until: Instant, statuses: Array<MessageStatus>, types: Array<MessageType>): List<MessageStatistic>
}

interface CustomizedPmxMessageRepository<T, ID> {
    fun <S : T?> save(entity: S): S
    fun findReadyToDispatch(now: Instant, page: PageRequest): List<PmxMessage>
    fun findQueuedByEngagementId(engagementId: String): List<PmxMessage>
    fun updateFields(id: String, fields: Map<String, Any?>): PmxMessage?
    fun getMostRecentByEngagementIdAndContact(engagementId: String, to: String): PmxMessage?
    fun getMostRecentlyDeliveredEngagementSmsMessage(to: String): PmxMessage?
}

@Repository
class CustomizedPmxMessageRepositoryImpl(private val mongoTemplate: MongoTemplate) :
    CustomizedPmxMessageRepository<PmxMessage, String> {
    override fun <S : PmxMessage?> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        entity!!.createdAt = entity.createdAt ?: Instant.now()
        entity.updatedAt = Instant.now()

        return mongoTemplate.save(entity)
    }

    override fun findReadyToDispatch(now: Instant, page: PageRequest): List<PmxMessage> {
        val nowEastern = now.atOffset(ZoneId.of("US/Eastern").rules.getOffset(now))
        val nowAtEpoch = InstantUtil.toEpochTime(nowEastern.toOffsetTime())

        val query = Query()
            .addCriteria(
                Criteria().andOperator(
                    Criteria.where("status").`is`(MessageStatus.QUEUED)
                        .and("sendAfter").lte(now),
                    Criteria().orOperator(
                        Criteria.where("sendWindow").isNull,
                        Criteria.where("sendWindow.from").lte(nowAtEpoch)
                            .and("sendWindow.until").gte(nowAtEpoch)
                    )
                )
            )
            .with(page)

        return mongoTemplate.find(query, PmxMessage::class.java)
    }

    override fun findQueuedByEngagementId(engagementId: String): List<PmxMessage> {
        val query = Query()
            .addCriteria(
                Criteria().andOperator(
                    Criteria.where("status").`is`(MessageStatus.QUEUED)
                        .and("engagementId").`is`(engagementId)
                )
            )

        return mongoTemplate.find(query, PmxMessage::class.java)
    }

    override fun updateFields(id: String, fields: Map<String, Any?>): PmxMessage? {
        val query = Query.query(Criteria.where("_id").`is`(id))

        val update = Update()
        fields.forEach { update.set(it.key, it.value) }
        return mongoTemplate.findAndModify(
            query,
            update,
            FindAndModifyOptions().returnNew(true),
            PmxMessage::class.java
        )
    }

    override fun getMostRecentByEngagementIdAndContact(engagementId: String, to: String): PmxMessage? {
        return mongoTemplate.findOne(
            Query()
                // note: in order to utilize the engagementId sparse index
                // DocumentDB requires the inclusion of {$exists: true}
                .addCriteria(Criteria.where("engagementId").exists(true))
                .addCriteria(
                    Criteria().andOperator(
                        Criteria.where("engagementId").`is`(engagementId)
                            .and("to").`is`(to)
                    )
                )
                .with(Sort.by(Sort.Direction.DESC, "completedAt"))
                .limit(1),
            PmxMessage::class.java
        )
    }

    override fun getMostRecentlyDeliveredEngagementSmsMessage(to: String): PmxMessage? {
        return mongoTemplate.findOne(
            Query()
                .addCriteria(
                    Criteria().andOperator(
                        Criteria.where("to").`is`(to)
                            .and("type").`is`(MessageType.SMS)
                            .and("status").`in`(MessageStatus.DELIVERED, MessageStatus.SENT)
                            .and("engagementId").exists(true)
                    )
                )
                .with(Sort.by(Sort.Direction.DESC, "completedAt"))
                .limit(1),
            PmxMessage::class.java
        )
    }
}