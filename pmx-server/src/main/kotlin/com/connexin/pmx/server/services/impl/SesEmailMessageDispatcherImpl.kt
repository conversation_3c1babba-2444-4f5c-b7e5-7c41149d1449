package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.PmxEmailTags.PMX_MESSAGE_ID_TAG
import com.connexin.pmx.server.models.PmxEmailTags.PMX_MESSAGE_REMOTE_ID
import com.connexin.pmx.server.models.PmxEmailTags.PMX_MESSAGE_TYPE_TAG
import com.connexin.pmx.server.models.aws.EmailEvent
import com.connexin.pmx.server.models.aws.EmailEvent.Bounce.BounceSubType
import com.connexin.pmx.server.models.aws.EmailEvent.Bounce.BounceType
import com.connexin.pmx.server.models.aws.EmailEvent.DeliveryDelay.DelayType
import com.connexin.pmx.server.models.aws.EmailEventType
import com.connexin.pmx.server.models.dtos.BeforeRespondResult
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult
import com.connexin.pmx.server.services.*
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import java.time.Duration
import java.util.*

class SesEmailMessageDispatcherImpl(
    private val emailService: EmailService,
    private val objectMapper: ObjectMapper,
    private val subscriptionService: SubscriptionService,
    private val urls: UrlGenerator,
    private val pmxMessageService: PmxMessageService,
    private val broadcastBatchSize: Int
) : EmailMessageDispatcher {

    companion object {
        private val log = LoggerFactory.getLogger(SesEmailMessageDispatcherImpl::class.java)
        val UNSUBSCRIBE_MSG_PLAIN_TEXT = mapOf(
            Language.ENGLISH to "If you would prefer not to receive further messages from this sender please follow the link and confirm your request. <Unsubscribe_Link>",
            Language.SPANISH to "Si prefiere no recibir más mensajes de este remitente, siga el enlace y confirme su solicitud. <Unsubscribe_Link>"
        )
    }

    override fun initiate(message: PmxMessage, customer: Customer): InitiateResult {
        return when (message.type) {
            MessageType.EMAIL -> initiateEmail(message)
            MessageType.EMAIL_BROADCAST -> initiateEmailBroadcast(message)
            else -> InitiateResult(success = false, errors = "The PMX message is not of an email type")
        }
    }

    override fun respond(context: RespondContext): RespondResult {
        val message = context.message!!
        val event = context.decodedPayload as EmailEvent? ?: return RespondResult(success = false)
        var reason: EmailDeliveryFailureReason? = null
        val messageStatus = when (event.eventType) {
            EmailEventType.Send -> {
                log.info("Email was sent")
                MessageStatus.SENT
            }
            EmailEventType.Delivery -> {
                log.info("Email was delivered")
                MessageStatus.DELIVERED
            }
            EmailEventType.DeliveryDelay -> {
                log.info("Email delivery is delayed: {}", event.deliveryDelay?.delayType?.name)
                reason = when (event.deliveryDelay?.delayType) {
                    DelayType.InternalFailure -> EmailDeliveryFailureReason.VENDOR_INTERNAL_ERROR
                    DelayType.MailboxFull -> EmailDeliveryFailureReason.MAILBOX_FULL
                    DelayType.SpamDetected -> EmailDeliveryFailureReason.SPAM_DETECTED
                    DelayType.RecipientServerError -> EmailDeliveryFailureReason.RECIPIENT_SERVER_ERROR
                    DelayType.IPFailure -> EmailDeliveryFailureReason.SENDING_IP_BLOCKED
                    DelayType.TransientCommunicationFailure -> EmailDeliveryFailureReason.COMMUNICATION_FAILURE
                    else -> EmailDeliveryFailureReason.UNDETERMINED
                }
                MessageStatus.FAILED
            }
            EmailEventType.Bounce -> {
                log.info("Email bounced: {}/{}", event.bounce?.bounceType, event.bounce?.bounceSubType)
                reason = when(event.bounce?.bounceType) {
                    BounceType.Permanent -> EmailDeliveryFailureReason.HARD_BOUNCE
                    BounceType.Transient -> when (event.bounce.bounceSubType) {
                        BounceSubType.General -> EmailDeliveryFailureReason.SOFT_BOUNCE
                        BounceSubType.MailboxFull -> EmailDeliveryFailureReason.MAILBOX_FULL
                        BounceSubType.MessageTooLarge -> EmailDeliveryFailureReason.MESSAGE_TOO_LARGE
                        BounceSubType.ContentRejected -> EmailDeliveryFailureReason.CONTENT_REJECTED
                        BounceSubType.AttachmentRejected -> EmailDeliveryFailureReason.ATTACHMENT_REJECTED
                        else -> EmailDeliveryFailureReason.UNDETERMINED
                    }
                    else -> EmailDeliveryFailureReason.UNDETERMINED
                }
                MessageStatus.FAILED
            }
            EmailEventType.Reject -> {
                log.info("Email was rejected: {}", event.reject?.reason)
                reason = EmailDeliveryFailureReason.CONTENT_REJECTED
                MessageStatus.FAILED
            }
        }
        // for EMAIL, status is set on the PMX message
        // for EMAIL_BROADCAST status is set on each destination
        if (message.type == MessageType.EMAIL) {
            message.status = messageStatus
            message.emailDeliveryFailureReason = reason
            message.errors = when (event.eventType) {
                EmailEventType.Bounce -> objectMapper.writeValueAsString(event.bounce)
                EmailEventType.DeliveryDelay -> objectMapper.writeValueAsString(event.deliveryDelay)
                EmailEventType.Reject -> objectMapper.writeValueAsString(event.reject)
                else -> null
            }
        } else if (message.type == MessageType.EMAIL_BROADCAST) {
            val destinations = message.emailRecipients!!
            val destination = destinations.find { it.remoteId == event.mail.messageId }
            if (destination != null) {
                destination.status = messageStatus
                destination.reason = reason
                destination.errors = when (event.eventType) {
                    EmailEventType.Bounce -> objectMapper.writeValueAsString(event.bounce)
                    EmailEventType.DeliveryDelay -> objectMapper.writeValueAsString(event.deliveryDelay)
                    EmailEventType.Reject -> objectMapper.writeValueAsString(event.reject)
                    else -> null
                }
            }
            // update the parent status once all are either delivered or failed
            if (destinations.all { it.status == MessageStatus.DELIVERED }) {
                message.status = MessageStatus.DELIVERED
            } else if (destinations.all { it.status == MessageStatus.FAILED }) {
                message.status = MessageStatus.FAILED
            }
        }
        return RespondResult(success = true)
    }

    override fun beforeRespond(context: RespondContext): BeforeRespondResult {
        val event = EmailEvent.parse(context.payload, objectMapper)
        val mailId = event?.mail?.messageId
        val remoteId = when (context.type) {
            MessageType.EMAIL -> mailId
            MessageType.EMAIL_BROADCAST -> event?.mail?.tags?.get(PMX_MESSAGE_REMOTE_ID)?.first()
            else -> {
                log.warn("Invalid messageType: {}", context.type)
                null
            }
        }
        var accepted = !remoteId.isNullOrEmpty()

        if (accepted) {
            val message = pmxMessageService.getByRemoteId(remoteId!!)
            if (message != null) {
                if (message.status == MessageStatus.DELIVERED) {
                    log.debug("Ignoring webhook for delivered message")
                    accepted = false
                } else if (context.type == MessageType.EMAIL_BROADCAST) {
                    val recipient = message.emailRecipients!!.find { it.remoteId == mailId }
                    if (recipient == null) {
                        log.warn("Unable to find email broadcast recipient with remoteId {}", mailId)
                        accepted = false
                    } else if (recipient.status == MessageStatus.DELIVERED) {
                        log.debug("Ignoring webhook for delivered message")
                        accepted = false
                    }
                }
            }
        }

        return BeforeRespondResult(
            success = true,
            accepted = accepted,
            context = context.copy(remoteId = remoteId, decodedPayload = event)
        )
    }

    private fun initiateEmail(message: PmxMessage): InitiateResult {
        // ensure the intended recipient is not unsubscribed
        val subscribed = subscriptionService.getById(message.to!!)?.subscribed
        if (subscribed == false) {

            log.debug("Recipient is unsubscribed")
            return InitiateResult(
                success = false,
                reason = InitiateResult.Reason.CONSENT,
                errors = "Email address is unsubscribed"
            )
        }

        val result = emailService.sendEmail(
            EmailMessage(
                subject = message.subject,
                body = buildMessage(message),
                replyToAddress = message.replyTo ?: message.from,
                toAddresses = mutableSetOf(message.to)
            ),
            tags = mapOf(
                PMX_MESSAGE_ID_TAG to message.id!!,
                PMX_MESSAGE_TYPE_TAG to message.type.toString()
            )
        )

        val initiateResult = InitiateResult(
            success = result.success,
            remoteId = result.messageId,
            errors = result.error,
            reason = if (result.retry == true && result.error == null) InitiateResult.Reason.RATE_LIMIT else null
        )
        return if (result.retry == true && result.retryDelay != null) {
            initiateResult.copy(retry = true, retryDelay = result.retryDelay)
        } else {
            initiateResult
        }
    }

    private fun initiateEmailBroadcast(message: PmxMessage): InitiateResult {
        if (message.emailRecipients.isNullOrEmpty()) {
            return InitiateResult(success = false, errors = "No destinations for email broadcast")
        }

        // we'll generate our own "remoteId" for the PMX message
        // since the email service does not provide one at the parent level
        if (message.remoteId.isNullOrEmpty()) {
            message.remoteId = UUID.randomUUID().toString()

            // store remoteId on the message *before* sending emails
            // so incoming webhooks can locate the message by remoteId
            pmxMessageService.patch(message.id!!, mapOf("remoteId" to message.remoteId))
        }

        val queuedEntries = getQueuedEntries(message)
        if (queuedEntries.isEmpty()) {
            // if we get here there are no more QUEUED entries within the broadcast that are not unsubscribed
            log.info("No queued destinations that we can send to")
            return InitiateResult(success = true, remoteId = message.remoteId)
        }

        val addressToIndexMap = message.emailRecipients.withIndex()
            .associate { (index, recipient) -> recipient.address to index }

        var updates = mutableMapOf<String, Any?>()
        var count = 0

        val result = emailService.sendBulkEmail(
            BulkEmailMessage(
                template = BulkEmailMessage.Template(
                    name = message.id!!,
                    subject = message.subject,
                    htmlContent = formatBroadcastMessage(message.message)
                ),
                replyToAddress = message.replyTo ?: message.from,
                entries = queuedEntries
            ),
            tags = mapOf(
                PMX_MESSAGE_ID_TAG to message.id,
                PMX_MESSAGE_TYPE_TAG to message.type.toString(),
                PMX_MESSAGE_REMOTE_ID to message.remoteId!!
            )
        ) { batchEntryResults ->
            // for email broadcasts we can't wait for the calling facade to update the entire PMX message at the end
            // because by then it's likely that incoming webhooks will have been received, and we'll overwrite the message delivery state.
            // to minimize this behavior, for now, i'm going to fetch and update the PMX message for each batch of recipients
            batchEntryResults.forEach { entryResult ->
                val index = addressToIndexMap[entryResult.toAddress]
                if (index != null) {
                    updates["emailRecipients.$index.remoteId"] = entryResult.messageId
                    updates["emailRecipients.$index.status"] = when {
                        entryResult.success -> MessageStatus.DISPATCHED
                        entryResult.retry == true -> MessageStatus.QUEUED
                        else -> MessageStatus.FAILED
                    }

                    // Commit updates periodically to handle webhooks that might arrive quickly
                    // Either patch when we have enough updates or when we reach the last entry
                    // We use a multiplier of 2 to account for the fact that we are updating two fields per entry
                    if (updates.size >= broadcastBatchSize * 2 || count >= addressToIndexMap.size - 1) {
                        pmxMessageService.patch(message.id, updates)
                        log.debug("Patched bulk email entries with following updates: {}", updates)
                        updates = mutableMapOf()
                    }
                }
                count++
            }
        }

        val retry = result.entryResults.any { it.retry == true }
        val retryDelay = if (retry) result.entryResults.maxOf { it.retryDelay ?: Duration.ZERO } else null
        val initiateResult = InitiateResult(
            success = result.success,
            remoteId = message.remoteId,
            errors = result.error
        )
        return if (retryDelay != null) {
            initiateResult.copy(retry = true, retryDelay = retryDelay)
        } else {
            initiateResult
        }
    }

    private fun getQueuedEntries(message: PmxMessage): List<BulkEmailMessage.Entry> {
        val entries = mutableListOf<BulkEmailMessage.Entry>()
        var update = false
        val queuedRecipients = message.emailRecipients?.filter { it.status == MessageStatus.QUEUED }
        if (queuedRecipients.isNullOrEmpty()) {
            return entries
        }
        val allEmailAddresses = queuedRecipients.map { it.address }.toSet()
        val subscriptionMap = subscriptionService.getByIds(allEmailAddresses).associateBy { it.id }

        queuedRecipients.forEach {
            val subscribed = subscriptionMap[it.address]?.subscribed
            if (subscribed != false) {
                entries.add(
                    BulkEmailMessage.Entry(
                        toAddress = it.address,
                        unsubscribeLink = urls.unsubscribe(
                            mapOf(
                                "id" to message.id!!,
                                "email" to it.address
                            )
                        ).toString()
                    )
                )
            } else {
                it.status = MessageStatus.FAILED
                it.reason = EmailDeliveryFailureReason.UNSUBSCRIBED
                update = true
            }
        }

        if (update) {
            log.debug("Updating message with unsubscribed recipients")
            pmxMessageService.update(message)
        }

        return entries
    }

    // Any future plaintext -> HTML specific formatting for Email Broadcasts can live here
    private fun formatBroadcastMessage(message: String): String {
        val htmlNewlines = message.replace("\n", "<br>")

        return htmlNewlines
    }

    fun buildMessage(message: PmxMessage): String {
        return "${message.message.trimEnd()}\n\n${
            UNSUBSCRIBE_MSG_PLAIN_TEXT[message.language ?: Language.ENGLISH]?.replace(
                "<Unsubscribe_Link>",
                urls.unsubscribe(mapOf("id" to message.id!!, "email" to message.to!!)).toString()
            )
        }"
    }
}