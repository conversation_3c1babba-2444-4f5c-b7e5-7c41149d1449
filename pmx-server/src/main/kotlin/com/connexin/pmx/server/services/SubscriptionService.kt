package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.SubscriptionPreference

interface SubscriptionService {
    /**
     * Retrieves the subscription preference for an address.
     * @param address The contact's address, such as an email or phone number.
     * @return The subscription preference whose ID matches address, or null if not found.
     */
    fun getById(address: String): SubscriptionPreference?

    /**
     * Retrieves the subscription preferences for a set of addresses.
     * @param addresses The contact's addresses, such as an email or phone number.
     * @return The subscription preferences whose IDs match the addresses.
     */
    fun getByIds(addresses: Collection<String>): Iterable<SubscriptionPreference>

    /**
     * Saves a subscription preference.
     * @param preference The preference entity to save.
     */
    fun save(preference: SubscriptionPreference): SubscriptionPreference
}