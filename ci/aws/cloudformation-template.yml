AWSTemplateFormatVersion: 2010-09-09

Description: 'Deploy a PMX service on AWS Fargate, hosted in a private subnet.'

Parameters:
  ClusterName:
    Type: String
    Description: The name of the ECS Cluster this service is being added to.
  EnvironmentName:
    Type: String
    Description: The name of the environment to add this service to. ie Prod,Staging,Dev
  AtlasSecret:
    Type: String
    Description: KMS name containing Atlas secrets assigned to PMX
  ServiceName:
    Type: String
    Description: The name of the service being deployed
  ServiceDiscovery:
    Type: String
    Description: The namespace ID of the Route 53 service discovery layer to place this service into
  VPCID:
    Description: Specify the VPC this service should be created in
    Type: 'AWS::EC2::VPC::Id'
  DefaultAppSG:
    Description: The Default Security Group that this service will be a member of
    Type: 'CommaDelimitedList'
  PrivateSubnets:
    Description: Ensure the subnets match the VPC that has been selected.
    Type: 'CommaDelimitedList'
  DesiredCount:
    Type: Number
    Description: The number of instantiations of the task definition to place and keep running in the cluster
  ImageUrl:
    Type: String
    Description: >-
      The url of a docker image that contains the application process that will
      handle the traffic for this service
  TaskCpu:
    Type: Number
    Description: The CPU limit for the entire task, should be higher than container CPUs
  TaskMemory:
    Type: Number
    Description: The memory limit for the entire task, should be higher than container memory
  ContainerCpu:
    Type: Number
    Description: How much CPU to give the container. 1024 is 1 CPU
  ContainerMemory:
    Type: Number
    Description: How much memory in megabytes to give the container
  ContainerPort:
    Type: String
    Description: The port the container's service is accessible over.
  Role:
    Type: String
    Description: >-
      (Optional) An IAM role to give the service's containers if the code within
      needs to access other AWS resources like S3 buckets, DynamoDB tables, etc
  SumoLambda:
    Type: String
    Description: Name of the Sumo Lambda Function used for log shipping to our sumo account.
  MongoSecret:
    Type: String
    Description: The name of the secret which contains the access info for MongoDB
  DockerSecret:
    Type: String
    Description: The name of the secret which contains auth credentials for our docker repo
  AwsSecret:
    Type: String
    Description: The name of the secret containing access info for AWS services
  TelnyxSecret:
    Type: String
    Description: The name of the secret containing Telnyx access info
  EnvDefaultFromEmailAddress:
    Type: String
    Description: The email address we are sending from
  EnvAwsRegion:
    Type: String
    Description: The AWS region
  EnvSesConfigurationSet:
    Type: String
    Description: The SES configuration set to use when sending emails
  DefaultLogLevel:
    Type: String
    Description: Default log level for the pmx namespace
  SpringBootAdminSecret:
    Type: String
    Description: The name of the secret containing Spring Boot Admin client connection details
  EnvSpringBootAdminClientEnabled:
    Type: String
    Description: Whether or not the Spring Boot Admin client should be enabled
  EnvSpringBootAdminClientUrl:
    Type: String
    Description: The URL the Spring Boot Admin client should connect to
  EnvServerPort:
    Type: Number
    Description: The port the service is hosted on
  TargetGroupArn:
    Type: String
    Description: The ARN of the load balance target group
  SnsSubscriptionEndpoint:
    Type: String
    Description: The https endpoint that is subscribed to the SNS topic
  SesConfigSetName:
    Type: String
    Description: The name of the SES configuration set to use when sending emails
  EnvPmxBaseUrl:
    Type: String
    Description: Base URL for accessing the service publicly
  EnvPmxDispatcherPageSize:
    Type: Number
    Description: Number of messages to dispatch per scheduled cycle
  EnvPmxDispatcherInitialDelay:
    Type: Number
    Description: Milliseconds of initial delay before the dispatcher sends queued messages
  EnvPmxDispatcherFixedDelay:
    Type: Number
    Description: Milliseconds between dispatcher cycles.
  DynatraceSecret:
    Type: String
    Description: The name of the secret for dynatrace
  EnvDynatraceEnabled:
    Type: String
    Description: Whether or not to enable dynatrace
  EnvPmxProvisioningPageSize:
    Type: Number
    Description: Number of orders to fetch at once when provisioning
  EnvPmxProvisioningInitialDelay:
    Type: Number
    Description: Milliseconds to wait when the service starts before fetching scheduled orders
  EnvPmxProvisioningFixedDelay:
    Type: Number
    Description: Milliseconds between checks for scheduled orders
  EnvPmxProvisioningDisableVendorCalls:
    Type: String
    Description: Whether or not performing provisioning actions with vendors is disabled.
  EnvPmxProvisioningMaxAttempts:
    Type: Number
    Description: Number of failed attempts when provisioning an order before it is considered failed
  EnvPmxEnvironment:
    Type: String
    Description: The environment (DEV, PREPROD, PROD)
  PmxUsersSecret:
    Type: String
    Description: Secret name containing passwords for well-known users
  EnvPmxCallTimeout:
    Type: Number
    Description: Seconds before voice call times out if no one answers
  EnvPmxCallTimeLimit:
    Type: Number
    Description: Maximum number of seconds a voice call cal last before automatically hanging up
  EnvCacheCustomersDuration:
    Type: Number
    Description: Duration in minutes customers are cached by ID
  EnvCacheCustomersMax:
    Type: Number
    Description: Max customers cached by ID
  EnvCacheCustomersByCredentialsDuration:
    Type: Number
    Description: Duration in minutes customers are cached by credentials
  EnvCacheCustomersByCredentialsMax:
    Type: Number
    Description: Max customers cached by credentials
  EnvCacheMessagesByRemoteIdDuration:
    Type: Number
    Description: Duration in minutes messages are cached by remote ID
  EnvCacheMessagesByRemoteIdMax:
    Type: Number
    Description: Max messages cached by remote ID
  EnvCacheWebhooksDuration:
    Type: Number
    Description: Duration in minutes webhooks are cached by event ID
  EnvCacheWebhooksMax:
    Type: Number
    Description: Max webhooks cached by event ID
  EnvCacheSesSendQuotaDuration:
    Type: Number
    Description: Duration in minutes SES send quota is cached
  EnvZipUrl:
    Type: String
    Description: URL string for zipcode API
  EnvCacheZipCodesDuration:
    Type: Number
    Description: Duration in minutes zip codes are cached
  EnvCacheZipCodesMax:
    Type: Number
    Description: Max zip codes cached
  EnvTelnyxEngagementEnglishMessagingProfileId:
    Type: String
    Description: Telnyx Messaging Profile ID to use for English PMX+ engagement messages.
  EnvTelnyxEngagementSpanishMessagingProfileId:
    Type: String
    Description: Telnyx Messaging Profile ID to use for Spanish PMX+ engagement messages.
  EnvTelnyxEngagementEnglishVoiceNumbers:
    Type: String
    Description: Telnyx voice numbers for English PMX+ engagement messages.
  EnvTelnyxEngagementSpanishVoiceNumbers:
    Type: String
    Description: Telnyx voice numbers for Spanish PMX+ engagement messages.
  EnvTelnyxEngagementCallControlConnectionId:
    Type: String
    Description: Telnyx Call Control connection ID for PMX+ engagement messages.
  EnvPmxConfirmationBaseUrl:
    Type: String
    Description: The base URL to use when constructing confirmation links.
  RedisSecret:
    Type: String
    Description: The name of the Redis secret
  EnvRedisEnabled:
    Type: String
    Description: Whether or not Redis should be enabled.
  SlackSecret:
    Type: String
    Description: Name of secret containing keys for Slack (notifications, etc.)
  EnvDeliveryStatsCron:
    Type: String
    Description: CRON expression for delivery stats job. Set to - to disable.
  ShortenerSecret:
    Type: String
    Description: KMS name containing OP URL Shortener secrets assigned to PMX
  PmxKeycloakSecret:
    Type: String
    Description: >-
      The name of the secret which contains the access info for pmx keycloak
  EnvKeycloakBridgeScope:
    Type: String
    Description: Pmx Keycloak Bridge Scopes
  EnvBridgeUrl:
    Type: String
    Description: OP Bridge Microservice Url
  EnvPmxEngagementSchedulerPageSize:
    Type: Number
    Description: Page size for the amount of engagement awaiting checkpoints to be processed on scheduled job
  EnvPmxEngagementSchedulerParallelBatchSize:
    Type: Number
    Description: Batch size of engagements awaiting checkpoints to be processed on coroutines
  EnvPmxEngagementDelayTimeSeconds:
    Type: Number
    Description: Minimum delay time (in seconds) of next checkpoint for a recently created engagement to avoid double processing on scheduled job vs creation job
  EnvPmxEmailBroadcastBatchSize:
    Type: Number
    Description: Batch size for the amount of email broadcasts to be updated when successfully sent
  EnvPmxDispatcherParallelSize:
    Type: Number
    Description: Sizer of the parallelism for dispatching messages, should be coherent with the delay time between messages for rate limiting
  EnvPmxDispatcherDelayTimeBetweenMessagesMs:
    Type: Number
    Description: Delay time between messages for dispatching messages, should be coherent with the parallelism size for rate limiting
  EnvCacheSubscriptionsByIdDuration:
    Type: Number
    Description: Duration in minutes subscriptions are cached by ID
  EnvCacheSubscriptionsByIdMax:
    Type: Number
    Description: Max subscriptions cached by ID

Conditions:
  HasCustomRole: !Not
    - !Equals [ !Ref Role, '' ]
  IsProd: !Equals [ !Ref EnvironmentName, 'Prod' ]
  IsDev: !Not [ Condition: IsProd ]

Resources:
  LogGroup:
    Type: 'AWS::Logs::LogGroup'
    Properties:
      LogGroupName: !Sub '/ecs/${ServiceName}-${EnvironmentName}'
      RetentionInDays: '3'

  TaskDefinition:
    Type: 'AWS::ECS::TaskDefinition'
    Properties:
      Family: !Sub '${ServiceName}-${EnvironmentName}'
      Cpu: !Ref TaskCpu
      Memory: !Ref TaskMemory
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/ecsTaskExecutionRole'
      TaskRoleArn: !If
        - HasCustomRole
        - !Sub 'arn:aws:iam::${AWS::AccountId}:role/${Role}'
        - !Ref 'AWS::NoValue'
      Tags:
        - Key: vnoc-rsg
          Value: !Sub '${ServiceName}-${EnvironmentName}'
      ContainerDefinitions:
        - Name: !Sub '${ServiceName}-${EnvironmentName}-Container'
          Cpu: !Ref ContainerCpu
          Memory: !Ref ContainerMemory
          Image: !Ref ImageUrl
          RepositoryCredentials:
            CredentialsParameter: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${DockerSecret}'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-multiline-pattern: '\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}.\d{3}\s-\s*[a-zA-Z]{3,5}'
              awslogs-stream-prefix: !Sub '${ServiceName}'
          PortMappings:
            - ContainerPort: !Ref ContainerPort
              HostPort: !Ref ContainerPort
              Protocol: TCP
          Secrets:
            - Name: ENV_MONGO_URI
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${MongoSecret}:uri::'
            - Name: spring.boot.admin.client.username
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${SpringBootAdminSecret}:username::'
            - Name: spring.boot.admin.client.password
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${SpringBootAdminSecret}:password::'
            - Name: ENV_AWS_ACCESS_KEY
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AwsSecret}:access-key::'
            - Name: ENV_AWS_SECRET_KEY
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AwsSecret}:secret-key::'
            - Name: ENV_TELNYX_API_KEY
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${TelnyxSecret}:api-key::'
            - Name: ENV_TELNYX_PUBLIC_KEY
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${TelnyxSecret}:public-key::'
            - Name: ENV_DYNATRACE_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${DynatraceSecret}:metrics-ingest-url::'
            - Name: ENV_DYNATRACE_API_TOKEN
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${DynatraceSecret}:token::'
            - Name: ENV_ADMIN_PASSWORD
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${PmxUsersSecret}:admin-password::'
            - Name: ENV_REDIS_NODES
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${RedisSecret}:NodeAddresses::'
            - Name: ENV_REDIS_PASSWORD
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${RedisSecret}:RedisAuth::'
            - Name: ENV_PMX_JOBS_DELIVERY_STATS_WEBHOOK_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${SlackSecret}:alerts-webhook::'
            - Name: ENV_SHORTENER_API_KEY
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ShortenerSecret}:api-key::'
            - Name: ENV_SHORTENER_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${ShortenerSecret}:url::'
            - Name: keycloak.token_url
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${PmxKeycloakSecret}:keycloak_token_url::'
            - Name: keycloak.client_id
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${PmxKeycloakSecret}:keycloak_client_id::'
            - Name: keycloak.client_secret
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${PmxKeycloakSecret}:keycloak_client_secret::'
            - Name: ENV_ATLAS_TOKEN_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:token-url::'
            - Name: ENV_ATLAS_CLIENT_ID
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:client-id::'
            - Name: ENV_ATLAS_CLIENT_SECRET
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:client-secret::'
            - Name: ENV_ATLAS_AUDIENCE
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:audience::'
            - Name: ENV_ATLAS_PRACTICE_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:practice-url::'
            - Name: ENV_ATLAS_SURVEY_URL
              ValueFrom: !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${AtlasSecret}:survey-url::'
          Environment:
            - Name: JAVA_TOOL_OPTIONS
              Value: -XX:+UseContainerSupport -XX:MaxRAMPercentage=85
            - Name: ENV_LOGGING_LEVEL_PMX
              Value: !Ref DefaultLogLevel
            - Name: ENV_SERVER_PORT
              Value: !Ref EnvServerPort
            - Name: ENV_AWS_REGION
              Value: !Ref EnvAwsRegion
            - Name: ENV_AWS_SES_CONFIGURATION_SET
              Value: !Ref EnvSesConfigurationSet
            - Name: ENV_DEFAULT_FROM_EMAIL_ADDRESS
              Value: !Ref EnvDefaultFromEmailAddress
            - Name: spring.boot.admin.client.enabled
              Value: !Ref EnvSpringBootAdminClientEnabled
            - Name: spring.boot.admin.client.instance.name
              Value: !Sub '${ServiceName}-${EnvironmentName}'
            - Name: spring.boot.admin.client.url
              Value: !Ref EnvSpringBootAdminClientUrl
            - Name: ENV_PMX_BASE_URL
              Value: !Ref EnvPmxBaseUrl
            - Name: ENV_PMX_DISPATCHER_PAGE_SIZE
              Value: !Ref EnvPmxDispatcherPageSize
            - Name: ENV_PMX_DISPATCHER_INITIAL_DELAY
              Value: !Ref EnvPmxDispatcherInitialDelay
            - Name: ENV_PMX_DISPATCHER_FIXED_DELAY
              Value: !Ref EnvPmxDispatcherFixedDelay
            - Name: ENV_DYNATRACE_ENABLED
              Value: !Ref EnvDynatraceEnabled
            - Name: ENV_PMX_PROVISIONING_PAGE_SIZE
              Value: !Ref EnvPmxProvisioningPageSize
            - Name: ENV_PMX_PROVISIONING_INITIAL_DELAY
              Value: !Ref EnvPmxProvisioningInitialDelay
            - Name: ENV_PMX_PROVISIONING_FIXED_DELAY
              Value: !Ref EnvPmxProvisioningFixedDelay
            - Name: ENV_PMX_PROVISIONING_DISABLE_VENDOR_CALLS
              Value: !Ref EnvPmxProvisioningDisableVendorCalls
            - Name: ENV_PMX_PROVISIONING_MAX_ATTEMPTS
              Value: !Ref EnvPmxProvisioningMaxAttempts
            - Name: ENV_PMX_ENVIROMENT
              Value: !Ref EnvPmxEnvironment
            - Name: ENV_PMX_CALL_TIMEOUT
              Value: !Ref EnvPmxCallTimeout
            - Name: ENV_PMX_CALL_TIME_LIMIT
              Value: !Ref EnvPmxCallTimeLimit
            - Name: ENV_CACHE_CUSTOMERS_DURATION
              Value: !Ref EnvCacheCustomersDuration
            - Name: ENV_CACHE_CUSTOMERS_MAX
              Value: !Ref EnvCacheCustomersMax
            - Name: ENV_CACHE_CUSTOMERS_BY_CREDENTIALS_DURATION
              Value: !Ref EnvCacheCustomersByCredentialsDuration
            - Name: ENV_CACHE_CUSTOMERS_BY_CREDENTIALS_MAX
              Value: !Ref EnvCacheCustomersByCredentialsMax
            - Name: ENV_CACHE_MESSAGES_BY_REMOTE_ID_DURATION
              Value: !Ref EnvCacheMessagesByRemoteIdDuration
            - Name: ENV_CACHE_MESSAGES_BY_REMOTE_ID_MAX
              Value: !Ref EnvCacheMessagesByRemoteIdMax
            - Name: ENV_CACHE_WEBHOOKS_DURATION
              Value: !Ref EnvCacheWebhooksDuration
            - Name: ENV_CACHE_WEBHOOKS_MAX
              Value: !Ref EnvCacheWebhooksMax
            - Name: ENV_CACHE_SES_SEND_QUOTA_DURATION
              Value: !Ref EnvCacheSesSendQuotaDuration
            - Name: ENV_ZIP_CODE_URL
              Value: !Ref EnvZipUrl
            - Name: ENV_CACHE_ZIP_CODES_DURATION
              Value: !Ref EnvCacheZipCodesDuration
            - Name: ENV_CACHE_ZIP_CODES_MAX
              Value: !Ref EnvCacheZipCodesMax
            - Name: ENV_TELNYX_ENGAGEMENT_ENGLISH_MESSAGING_PROFILE_ID
              Value: !Ref EnvTelnyxEngagementEnglishMessagingProfileId
            - Name: ENV_TELNYX_ENGAGEMENT_SPANISH_MESSAGING_PROFILE_ID
              Value: !Ref EnvTelnyxEngagementSpanishMessagingProfileId
            - Name: ENV_TELNYX_ENGAGEMENT_ENGLISH_VOICE_NUMBERS
              Value: !Ref EnvTelnyxEngagementEnglishVoiceNumbers
            - Name: ENV_TELNYX_ENGAGEMENT_SPANISH_VOICE_NUMBERS
              Value: !Ref EnvTelnyxEngagementSpanishVoiceNumbers
            - Name: ENV_TELNYX_ENGAGEMENT_CALL_CONTROL_CONNECTION_ID
              Value: !Ref EnvTelnyxEngagementCallControlConnectionId
            - Name: ENV_PMX_CONFIRMATION_BASE_URL
              Value: !Ref EnvPmxConfirmationBaseUrl
            - Name: ENV_REDIS_ENABLED
              Value: !Ref EnvRedisEnabled
            - Name: ENV_PMX_JOBS_DELIVERY_STATS_CRON
              Value: !Ref EnvDeliveryStatsCron
            - Name: keycloak.bridge_scope
              Value: !Ref EnvKeycloakBridgeScope
            - Name: ENV_BRIDGE_URL
              Value: !Ref EnvBridgeUrl
            - Name: ENV_PMX_ENGAGEMENT_SCHEDULER_PAGE_SIZE
              Value: !Ref EnvPmxEngagementSchedulerPageSize
            - Name: ENV_PMX_ENGAGEMENT_SCHEDULER_PARALLEL_BATCH_SIZE
              Value: !Ref EnvPmxEngagementSchedulerParallelBatchSize
            - Name: ENV_PMX_ENGAGEMENT_DELAY_TIME_SECONDS
              Value: !Ref EnvPmxEngagementDelayTimeSeconds
            - Name: ENV_PMX_EMAIL_BROADCAST_BATCH_SIZE
              Value: !Ref EnvPmxEmailBroadcastBatchSize
            - Name: ENV_PMX_DISPATCHER_PARALLEL_SIZE
              Value: !Ref EnvPmxDispatcherParallelSize
            - Name: ENV_PMX_DISPATCHER_DELAY_TIME_BETWEEN_MESSAGES_MS
              Value: !Ref EnvPmxDispatcherDelayTimeBetweenMessagesMs
            - Name: ENV_CACHE_SUBSCRIPTIONS_BY_ID_DURATION
              Value: !Ref EnvCacheSubscriptionsByIdDuration
            - Name: ENV_CACHE_SUBSCRIPTIONS_BY_ID_MAX
              Value: !Ref EnvCacheSubscriptionsByIdMax

  Service:
    Type: "AWS::ECS::Service"
    Properties:
      ServiceName: !Sub '${ServiceName}-${EnvironmentName}'
      Cluster: !Sub '${ClusterName}-${EnvironmentName}'
      LaunchType: FARGATE
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 75
      DesiredCount: !Ref DesiredCount
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups: !Ref DefaultAppSG
          Subnets: !Ref PrivateSubnets
      TaskDefinition: !Ref TaskDefinition
      PropagateTags: TASK_DEFINITION
      ServiceRegistries:
        - RegistryArn: !GetAtt DNSDiscovery.Arn

  DNSDiscovery:
    Type: 'AWS::ServiceDiscovery::Service'
    Properties:
      Name: !Sub '${ServiceName}'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: '10'
        NamespaceId: !Ref ServiceDiscovery
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Allow lambda to subscribe to logs
  AddLambdaPermission:
    Type: 'AWS::Lambda::Permission'
    DependsOn: LogGroup
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${SumoLambda}'
      Principal: logs.us-east-1.amazonaws.com
      SourceArn: !GetAtt
        - LogGroup
        - Arn

  SumoLambdaSubscription:
    Type: 'AWS::Logs::SubscriptionFilter'
    DependsOn:
      - LogGroup
      - AddLambdaPermission
    Properties:
      LogGroupName: !Ref LogGroup
      FilterPattern: ''
      DestinationArn: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${SumoLambda}'

  SNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: "pmx-ses-notifications"

  SNSSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      Endpoint: !Ref SnsSubscriptionEndpoint
      Protocol: https
      RawMessageDelivery: true
      TopicArn: !Ref SNSTopic

  # Note: You can't specify SNS event destinations for configuration sets in CloudFormation; is configured manually in AWS
  SESConfigSet:
    Type: AWS::SES::ConfigurationSet
    Properties:
      Name: !Ref SesConfigSetName