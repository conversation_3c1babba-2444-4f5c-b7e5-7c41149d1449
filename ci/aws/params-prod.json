[{"ParameterKey": "ClusterName", "ParameterValue": "Atlas-3rd-<PERSON>"}, {"ParameterKey": "EnvironmentName", "ParameterValue": "Prod"}, {"ParameterKey": "ServiceName", "ParameterValue": "PMX"}, {"ParameterKey": "ServiceDiscovery", "ParameterValue": "ns-w5fmcuypconlf2ji"}, {"ParameterKey": "VPCID", "ParameterValue": "vpc-864599e3"}, {"ParameterKey": "DefaultAppSG", "ParameterValue": "sg-03986184aee88514b"}, {"ParameterKey": "PrivateSubnets", "ParameterValue": "subnet-28a8cc04,subnet-351e826f"}, {"ParameterKey": "DesiredCount", "ParameterValue": "1"}, {"ParameterKey": "ImageUrl", "ParameterValue": "officepracticum/pmx:__DOCKER_TAG__"}, {"ParameterKey": "TaskCpu", "ParameterValue": "2048"}, {"ParameterKey": "TaskMemory", "ParameterValue": "4096"}, {"ParameterKey": "ContainerCpu", "ParameterValue": "2048"}, {"ParameterKey": "Container<PERSON><PERSON>ory", "ParameterValue": "4096"}, {"ParameterKey": "ContainerPort", "ParameterValue": "8080"}, {"ParameterKey": "Role", "ParameterValue": "ecsTaskExecutionRole"}, {"ParameterKey": "SumoLambda", "ParameterValue": "OPService-Logs-2-Sumo"}, {"ParameterKey": "MongoSecret", "ParameterValue": "prod/atlas-pmx/mongo-DvO6F9"}, {"ParameterKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ParameterValue": "docker_hub_creds-6FqHUm"}, {"ParameterKey": "AwsSecret", "ParameterValue": "prod/atlas-pmx/aws-xpn21j"}, {"ParameterKey": "EnvSesConfigurationSet", "ParameterValue": "pmx"}, {"ParameterKey": "EnvDefaultFromEmailAddress", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "EnvAwsRegion", "ParameterValue": "us-east-1"}, {"ParameterKey": "DefaultLogLevel", "ParameterValue": "DEBUG"}, {"ParameterKey": "EnvServerPort", "ParameterValue": "8080"}, {"ParameterKey": "SpringBootAdminSecret", "ParameterValue": "prod/atlas/springbootadmin-IQf1bS"}, {"ParameterKey": "EnvSpringBootAdminClientEnabled", "ParameterValue": "true"}, {"ParameterKey": "EnvSpringBootAdminClientUrl", "ParameterValue": "https://cloud-control.op.healthcare/"}, {"ParameterKey": "TargetGroupArn", "ParameterValue": "arn:aws:elasticloadbalancing:us-east-1:656099734204:targetgroup/Atlas-PMX-Prod/982930d63c1ee933"}, {"ParameterKey": "SnsSubscriptionEndpoint", "ParameterValue": "https://pmx.op.healthcare/webhooks/pmx/email/events"}, {"ParameterKey": "SesConfigSetName", "ParameterValue": "pmx"}, {"ParameterKey": "TelnyxSecret", "ParameterValue": "prod/atlas-pmx/telnyx-hCbbzl"}, {"ParameterKey": "EnvPmxBaseUrl", "ParameterValue": "https://pmx.op.healthcare"}, {"ParameterKey": "EnvPmxDispatcherPageSize", "ParameterValue": "1000"}, {"ParameterKey": "EnvPmxDispatcherInitialDelay", "ParameterValue": "20000"}, {"ParameterKey": "EnvPmxDispatcherFixedDelay", "ParameterValue": "20000"}, {"ParameterKey": "DynatraceSecret", "ParameterValue": "prod/dynatrace/api-NmmtjC"}, {"ParameterKey": "EnvDynatraceEnabled", "ParameterValue": "false"}, {"ParameterKey": "EnvPmxProvisioningPageSize", "ParameterValue": "50"}, {"ParameterKey": "EnvPmxProvisioningInitialDelay", "ParameterValue": "20000"}, {"ParameterKey": "EnvPmxProvisioningFixedDelay", "ParameterValue": "60000"}, {"ParameterKey": "EnvPmxProvisioningDisableVendorCalls", "ParameterValue": "false"}, {"ParameterKey": "EnvPmxProvisioningMaxAttempts", "ParameterValue": "5"}, {"ParameterKey": "EnvPmxEnvironment", "ParameterValue": "PROD"}, {"ParameterKey": "PmxUsersSecret", "ParameterValue": "prod/atlas-pmx/users-D6VBM5"}, {"ParameterKey": "EnvPmxCallTimeout", "ParameterValue": "120"}, {"ParameterKey": "EnvPmxCallTimeLimit", "ParameterValue": "3600"}, {"ParameterKey": "EnvCacheCustomersDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheCustomersMax", "ParameterValue": "500"}, {"ParameterKey": "EnvCacheCustomersByCredentialsDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheCustomersByCredentialsMax", "ParameterValue": "500"}, {"ParameterKey": "EnvCacheMessagesByRemoteIdDuration", "ParameterValue": "5"}, {"ParameterKey": "EnvCacheMessagesByRemoteIdMax", "ParameterValue": "1000"}, {"ParameterKey": "EnvCacheWebhooksDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheWebhooksMax", "ParameterValue": "200000"}, {"ParameterKey": "EnvCacheSesSendQuotaDuration", "ParameterValue": "30"}, {"ParameterKey": "EnvZipUrl", "ParameterValue": "https://kczm6uix68.execute-api.us-east-1.amazonaws.com/zip"}, {"ParameterKey": "EnvTelnyxEngagementEnglishMessagingProfileId", "ParameterValue": "40018728-e91b-4c2c-8e7a-2c08d76d77db"}, {"ParameterKey": "EnvTelnyxEngagementSpanishMessagingProfileId", "ParameterValue": "40018776-33ea-44d1-81c9-995efc1a1add"}, {"ParameterKey": "EnvTelnyxEngagementEnglishVoiceNumbers", "ParameterValue": "+18556542681"}, {"ParameterKey": "EnvTelnyxEngagementSpanishVoiceNumbers", "ParameterValue": "+18556041055"}, {"ParameterKey": "EnvTelnyxEngagementCallControlConnectionId", "ParameterValue": "2142200024698717255"}, {"ParameterKey": "EnvPmxConfirmationBaseUrl", "ParameterValue": "https://www.healthconfirmations.com"}, {"ParameterKey": "EnvCacheZipCodesDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheZipCodesMax", "ParameterValue": "2000"}, {"ParameterKey": "RedisSecret", "ParameterValue": "/prod/PMXPlus-Redis-rJqrg1"}, {"ParameterKey": "EnvRedisEnabled", "ParameterValue": "true"}, {"ParameterKey": "SlackSecret", "ParameterValue": "prod/pmx/slack-IdryFn"}, {"ParameterKey": "EnvDeliveryStatsCron", "ParameterValue": "0 0 * * * *"}, {"ParameterKey": "ShortenerSecret", "ParameterValue": "prod/pmx/shortener-yIzsuF"}, {"ParameterKey": "PmxKeycloakSecret", "ParameterValue": "prod/pmx/keycloak-gXgpna"}, {"ParameterKey": "EnvKeycloakBridgeScope", "ParameterValue": "read:bridge write:bridge"}, {"ParameterKey": "EnvBridgeUrl", "ParameterValue": "https://applications.op.healthcare/bridge"}, {"ParameterKey": "EnvPmxEngagementSchedulerPageSize", "ParameterValue": "1000"}, {"ParameterKey": "EnvPmxEngagementSchedulerParallelBatchSize", "ParameterValue": "100"}, {"ParameterKey": "EnvPmxEngagementDelayTimeSeconds", "ParameterValue": "600"}, {"ParameterKey": "AtlasSecret", "ParameterValue": "prod/pmx/atlas-VpHzka"}, {"ParameterKey": "EnvPmxEmailBroadcastBatchSize", "ParameterValue": "10"}, {"ParameterKey": "EnvPmxDispatcherParallelSize", "ParameterValue": "4"}, {"ParameterKey": "EnvPmxDispatcherDelayTimeBetweenMessagesMs", "ParameterValue": "250"}, {"ParameterKey": "EnvCacheSubscriptionsByIdDuration", "ParameterValue": "10080"}, {"ParameterKey": "EnvCacheSubscriptionsByIdMax", "ParameterValue": "50000"}]